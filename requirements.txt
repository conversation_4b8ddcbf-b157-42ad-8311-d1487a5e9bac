# 数据库驱动
pymysql>=1.1.0,<2.0.0
psycopg2-binary>=2.9.7,<3.0.0
pyodbc>=4.0.39,<5.0.0

# 异步数据库驱动
aiomysql>=0.2.0,<1.0.0
asyncpg>=0.29.0,<1.0.0

# 核心依赖
sqlalchemy>=2.0.21,<3.0.0
click>=8.1.7,<9.0.0
pyyaml>=6.0.1,<7.0.0
tqdm>=4.66.1,<5.0.0
colorama>=0.4.6,<1.0.0
tabulate>=0.9.0,<1.0.0
python-dotenv>=1.0.0,<2.0.0

# Web界面依赖
fastapi>=0.104.1,<1.0.0
uvicorn[standard]>=0.24.0,<1.0.0
websockets>=12.0,<13.0
pydantic>=2.5.0,<3.0.0
jinja2>=3.1.2,<4.0.0

# 安全和加密
cryptography>=41.0.0,<42.0.0

# 异步文件操作
aiofiles>=23.2.1,<24.0.0

# 缓存和会话
redis>=5.0.0,<6.0.0
aioredis>=2.0.1,<3.0.0

# 监控和指标
prometheus-client>=0.19.0,<1.0.0

# 可选依赖（用于特定数据库）
cx-Oracle>=8.3.0,<9.0.0

# 开发和测试依赖（可选）
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.1.0,<5.0.0
black>=23.0.0,<24.0.0
flake8>=6.0.0,<7.0.0
mypy>=1.5.0,<2.0.0
