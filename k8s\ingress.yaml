# Kubernetes Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rds-migrate-ingress
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - rds-migrate.example.com
    secretName: rds-migrate-tls
  rules:
  - host: rds-migrate.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rds-migrate-service
            port:
              number: 8000
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: rds-migrate-service
            port:
              number: 8080

---
# 内部管理Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rds-migrate-admin-ingress
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: admin
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: rds-migrate-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "RDS Migration Tool Admin"
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
spec:
  tls:
  - hosts:
    - admin.rds-migrate.example.com
    secretName: rds-migrate-admin-tls
  rules:
  - host: admin.rds-migrate.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rds-migrate-service
            port:
              number: 8000
