"""
高性能异步数据库迁移引擎
使用asyncio和异步数据库驱动提升性能
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator
from dataclasses import dataclass
import aiomysql
import asyncpg
from concurrent.futures import ThreadPoolExecutor
from .config import MigrationConfig
from .migrator import MigrationStats

logger = logging.getLogger(__name__)


@dataclass
class AsyncMigrationStats(MigrationStats):
    """异步迁移统计信息"""
    concurrent_connections: int = 0
    avg_batch_time: float = 0.0


class AsyncDatabaseManager:
    """异步数据库管理器"""
    
    def __init__(self, config):
        self.config = config
        self.pool = None
    
    async def create_pool(self):
        """创建连接池"""
        if self.config.db_type == 'mysql':
            self.pool = await aiomysql.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                db=self.config.database,
                charset=self.config.charset or 'utf8mb4',
                minsize=1,
                maxsize=10
            )
        elif self.config.db_type == 'postgresql':
            self.pool = await asyncpg.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                database=self.config.database,
                min_size=1,
                max_size=10
            )
        else:
            raise ValueError(f"异步模式暂不支持数据库类型: {self.config.db_type}")
    
    async def close_pool(self):
        """关闭连接池"""
        if self.pool:
            if self.config.db_type == 'mysql':
                self.pool.close()
                await self.pool.wait_closed()
            elif self.config.db_type == 'postgresql':
                await self.pool.close()
    
    async def execute_query(self, query: str, params: Optional[tuple] = None):
        """执行查询"""
        if self.config.db_type == 'mysql':
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(query, params)
                    return await cursor.fetchall()
        elif self.config.db_type == 'postgresql':
            async with self.pool.acquire() as conn:
                return await conn.fetch(query, *(params or ()))
    
    async def execute_many(self, query: str, params_list: List[tuple]):
        """批量执行"""
        if self.config.db_type == 'mysql':
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.executemany(query, params_list)
                    await conn.commit()
        elif self.config.db_type == 'postgresql':
            async with self.pool.acquire() as conn:
                await conn.executemany(query, params_list)
    
    async def get_table_list(self) -> List[str]:
        """获取表列表"""
        if self.config.db_type == 'mysql':
            query = "SHOW TABLES"
            result = await self.execute_query(query)
            return [row[0] for row in result]
        elif self.config.db_type == 'postgresql':
            query = """
                SELECT tablename FROM pg_tables 
                WHERE schemaname = 'public'
            """
            result = await self.execute_query(query)
            return [row['tablename'] for row in result]
    
    async def get_row_count(self, table_name: str) -> int:
        """获取表行数"""
        query = f"SELECT COUNT(*) FROM {table_name}"
        result = await self.execute_query(query)
        return result[0][0] if self.config.db_type == 'mysql' else result[0]['count']
    
    async def fetch_batch(self, table_name: str, columns: List[str], 
                         offset: int, batch_size: int) -> List[tuple]:
        """获取数据批次"""
        column_list = ', '.join(columns)
        query = f"SELECT {column_list} FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
        return await self.execute_query(query)


class AsyncMigrationEngine:
    """高性能异步迁移引擎"""
    
    def __init__(self, config: MigrationConfig):
        self.config = config
        self.source_db = None
        self.target_db = None
        self.stats = AsyncMigrationStats()
        self.progress_callback: Optional[Callable] = None
        self.executor = ThreadPoolExecutor(max_workers=config.parallel_workers)
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调"""
        self.progress_callback = callback
    
    async def connect_databases(self):
        """连接数据库"""
        logger.info("创建异步数据库连接...")
        
        # 只有MySQL和PostgreSQL支持异步
        if (self.config.source.db_type not in ['mysql', 'postgresql'] or 
            self.config.target.db_type not in ['mysql', 'postgresql']):
            raise ValueError("异步模式只支持MySQL和PostgreSQL")
        
        self.source_db = AsyncDatabaseManager(self.config.source)
        self.target_db = AsyncDatabaseManager(self.config.target)
        
        await self.source_db.create_pool()
        await self.target_db.create_pool()
        
        logger.info("异步数据库连接已建立")
    
    async def disconnect_databases(self):
        """断开数据库连接"""
        if self.source_db:
            await self.source_db.close_pool()
        if self.target_db:
            await self.target_db.close_pool()
        
        self.executor.shutdown(wait=True)
        logger.info("数据库连接已关闭")
    
    async def get_tables_to_migrate(self) -> List[str]:
        """获取要迁移的表列表"""
        all_tables = await self.source_db.get_table_list()
        
        if self.config.tables:
            tables = [t for t in self.config.tables if t in all_tables]
            missing = set(self.config.tables) - set(tables)
            if missing:
                logger.warning(f"源数据库中未找到表: {missing}")
        else:
            tables = all_tables
        
        if self.config.exclude_tables:
            tables = [t for t in tables if t not in self.config.exclude_tables]
        
        logger.info(f"将迁移 {len(tables)} 个表")
        return tables
    
    async def migrate_table_data_async(self, table_name: str, columns: List[str]) -> bool:
        """异步迁移表数据"""
        try:
            logger.info(f"开始异步迁移表数据: {table_name}")
            
            total_rows = await self.source_db.get_row_count(table_name)
            if total_rows == 0:
                logger.info(f"表 {table_name} 为空，跳过数据迁移")
                return True
            
            batch_size = self.config.batch_size
            migrated_rows = 0
            batch_times = []
            
            # 创建插入语句
            column_list = ', '.join(columns)
            placeholders = ', '.join(['%s'] * len(columns))
            insert_query = f"INSERT INTO {table_name} ({column_list}) VALUES ({placeholders})"
            
            # 并发处理批次
            semaphore = asyncio.Semaphore(self.config.parallel_workers)
            tasks = []
            
            for offset in range(0, total_rows, batch_size):
                task = self._process_batch_async(
                    semaphore, table_name, columns, offset, batch_size, 
                    insert_query, batch_times
                )
                tasks.append(task)
            
            # 等待所有批次完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"批次处理失败: {result}")
                    self.stats.errors += 1
                else:
                    migrated_rows += result
            
            # 更新统计信息
            self.stats.rows_migrated += migrated_rows
            if batch_times:
                self.stats.avg_batch_time = sum(batch_times) / len(batch_times)
            
            logger.info(f"表 {table_name} 数据迁移完成: {migrated_rows} 行")
            return True
            
        except Exception as e:
            logger.error(f"表 {table_name} 数据迁移失败: {str(e)}")
            self.stats.errors += 1
            return False
    
    async def _process_batch_async(self, semaphore: asyncio.Semaphore, 
                                  table_name: str, columns: List[str],
                                  offset: int, batch_size: int, 
                                  insert_query: str, batch_times: List[float]) -> int:
        """异步处理单个批次"""
        async with semaphore:
            start_time = time.time()
            
            try:
                # 获取数据
                batch_data = await self.source_db.fetch_batch(
                    table_name, columns, offset, batch_size
                )
                
                if not batch_data:
                    return 0
                
                # 插入数据
                await self.target_db.execute_many(insert_query, batch_data)
                
                batch_time = time.time() - start_time
                batch_times.append(batch_time)
                
                # 更新进度
                if self.progress_callback:
                    self.progress_callback(table_name, len(batch_data), offset + len(batch_data))
                
                return len(batch_data)
                
            except Exception as e:
                logger.error(f"批次处理失败 (offset={offset}): {str(e)}")
                raise
    
    async def migrate_table_async(self, table_name: str) -> bool:
        """异步迁移单个表"""
        success = True
        
        # 获取表结构（这部分仍使用同步方式）
        if not self.config.data_only:
            # 表结构迁移需要使用同步方式
            from .migrator import MigrationEngine
            sync_engine = MigrationEngine(self.config)
            sync_engine.connect_databases()
            success &= sync_engine.migrate_table_schema(table_name)
            sync_engine.disconnect_databases()
        
        # 异步迁移数据
        if not self.config.schema_only and success:
            # 获取列信息（简化版本）
            columns = ['*']  # 实际应用中需要获取具体列名
            success &= await self.migrate_table_data_async(table_name, columns)
        
        if success:
            self.stats.tables_migrated += 1
        
        return success
    
    async def migrate_async(self) -> AsyncMigrationStats:
        """执行异步迁移"""
        self.stats.start_time = time.time()
        
        try:
            logger.info("开始异步数据库迁移...")
            
            await self.connect_databases()
            
            tables = await self.get_tables_to_migrate()
            if not tables:
                logger.warning("没有要迁移的表")
                return self.stats
            
            # 并发迁移表
            tasks = [self.migrate_table_async(table) for table in tables]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"表迁移失败: {result}")
                    self.stats.errors += 1
            
            logger.info("异步迁移完成")
            
        except Exception as e:
            logger.error(f"异步迁移失败: {str(e)}")
            self.stats.errors += 1
        
        finally:
            await self.disconnect_databases()
            self.stats.end_time = time.time()
        
        return self.stats


# 使用示例
async def run_async_migration(config: MigrationConfig):
    """运行异步迁移"""
    engine = AsyncMigrationEngine(config)
    stats = await engine.migrate_async()
    return stats
