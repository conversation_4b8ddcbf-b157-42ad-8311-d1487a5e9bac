"""
高性能异步数据库迁移引擎
使用asyncio和异步数据库驱动提升性能，优化版本包含增强的类型安全和错误处理
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator, Union, Protocol
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
import aiomysql
import asyncpg
from concurrent.futures import ThreadPoolExecutor
from .config import MigrationConfig, DatabaseConfig
from .migrator import MigrationStats

logger = logging.getLogger(__name__)


class AsyncConnectionPool(Protocol):
    """异步连接池协议"""
    async def acquire(self) -> Any: ...
    def release(self, connection: Any) -> None: ...
    async def close(self) -> None: ...


@dataclass
class AsyncMigrationStats(MigrationStats):
    """
    异步迁移统计信息，包含性能指标和并发统计
    """
    concurrent_connections: int = 0
    avg_batch_time: float = 0.0
    peak_memory_usage: float = 0.0
    connection_pool_stats: Dict[str, int] = field(default_factory=dict)
    async_operation_times: Dict[str, List[float]] = field(default_factory=dict)

    @property
    def avg_connection_utilization(self) -> float:
        """计算平均连接利用率"""
        if self.concurrent_connections == 0:
            return 0.0
        active = self.connection_pool_stats.get('active', 0)
        return (active / self.concurrent_connections) * 100.0


class AsyncDatabaseManager:
    """
    异步数据库管理器，优化版本包含连接池管理、健康检查和错误恢复
    """

    def __init__(self, config: DatabaseConfig) -> None:
        self.config = config
        self.pool: Optional[AsyncConnectionPool] = None
        self._pool_stats = {
            'created_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'total_queries': 0
        }
        self._health_check_interval = 30.0
        self._health_check_task: Optional[asyncio.Task] = None
    
    async def create_pool(self, min_size: int = 2, max_size: int = 10) -> None:
        """
        创建优化的连接池，支持健康检查和错误恢复

        Args:
            min_size: 最小连接数
            max_size: 最大连接数

        Raises:
            ValueError: 不支持的数据库类型
            ConnectionError: 连接池创建失败
        """
        try:
            if self.config.db_type == 'mysql':
                self.pool = await self._create_mysql_pool(min_size, max_size)
            elif self.config.db_type == 'postgresql':
                self.pool = await self._create_postgresql_pool(min_size, max_size)
            else:
                raise ValueError(f"异步模式暂不支持数据库类型: {self.config.db_type}")

            # 启动健康检查
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            self._pool_stats['created_connections'] = max_size

            logger.info(f"异步连接池创建成功 ({self.config.db_type}): {min_size}-{max_size} 连接")

        except Exception as e:
            logger.error(f"创建连接池失败: {e}")
            raise ConnectionError(f"Failed to create connection pool: {e}") from e

    async def _create_mysql_pool(self, min_size: int, max_size: int) -> Any:
        """创建MySQL连接池"""
        try:
            return await aiomysql.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                db=self.config.database,
                charset=self.config.charset or 'utf8mb4',
                minsize=min_size,
                maxsize=max_size,
                autocommit=False,
                echo=False,
                pool_recycle=3600,  # 1小时回收连接
                connect_timeout=30
            )
        except Exception as e:
            raise ConnectionError(f"MySQL连接池创建失败: {e}") from e

    async def _create_postgresql_pool(self, min_size: int, max_size: int) -> Any:
        """创建PostgreSQL连接池"""
        try:
            return await asyncpg.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                database=self.config.database,
                min_size=min_size,
                max_size=max_size,
                command_timeout=60,
                server_settings={
                    'application_name': 'rds_migrator',
                    'tcp_keepalives_idle': '600',
                    'tcp_keepalives_interval': '30',
                    'tcp_keepalives_count': '3'
                }
            )
        except Exception as e:
            raise ConnectionError(f"PostgreSQL连接池创建失败: {e}") from e

    async def close_pool(self) -> None:
        """关闭连接池和健康检查任务"""
        # 停止健康检查
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

        # 关闭连接池
        if self.pool:
            try:
                if self.config.db_type == 'mysql':
                    self.pool.close()
                    await self.pool.wait_closed()
                elif self.config.db_type == 'postgresql':
                    await self.pool.close()

                logger.info("异步连接池已关闭")
            except Exception as e:
                logger.warning(f"关闭连接池时出错: {e}")

    @asynccontextmanager
    async def get_connection(self):
        """获取连接的上下文管理器"""
        if not self.pool:
            raise RuntimeError("连接池未初始化")

        connection = None
        try:
            connection = await self.pool.acquire()
            self._pool_stats['active_connections'] += 1
            yield connection
        except Exception as e:
            self._pool_stats['failed_connections'] += 1
            logger.error(f"获取连接失败: {e}")
            raise
        finally:
            if connection:
                try:
                    self.pool.release(connection)
                    self._pool_stats['active_connections'] -= 1
                except Exception as e:
                    logger.warning(f"释放连接失败: {e}")

    async def execute_query(self, query: str, params: Optional[Union[tuple, List]] = None) -> List[Any]:
        """
        执行查询，支持参数化查询和错误处理

        Args:
            query: SQL查询语句
            params: 查询参数

        Returns:
            查询结果列表
        """
        start_time = time.time()

        try:
            async with self.get_connection() as conn:
                if self.config.db_type == 'mysql':
                    async with conn.cursor() as cursor:
                        await cursor.execute(query, params)
                        result = await cursor.fetchall()
                elif self.config.db_type == 'postgresql':
                    if params:
                        result = await conn.fetch(query, *params)
                    else:
                        result = await conn.fetch(query)
                else:
                    raise ValueError(f"不支持的数据库类型: {self.config.db_type}")

                self._pool_stats['total_queries'] += 1
                query_time = time.time() - start_time
                logger.debug(f"查询执行完成 ({query_time:.3f}s): {query[:100]}...")

                return result

        except Exception as e:
            logger.error(f"查询执行失败: {e}, SQL: {query[:100]}...")
            raise

    async def execute_many(self, query: str, params_list: List[Union[tuple, List]]) -> None:
        """
        批量执行，优化性能和错误处理

        Args:
            query: SQL语句
            params_list: 参数列表
        """
        if not params_list:
            return

        start_time = time.time()

        try:
            async with self.get_connection() as conn:
                if self.config.db_type == 'mysql':
                    async with conn.cursor() as cursor:
                        await cursor.executemany(query, params_list)
                        await conn.commit()
                elif self.config.db_type == 'postgresql':
                    await conn.executemany(query, params_list)
                else:
                    raise ValueError(f"不支持的数据库类型: {self.config.db_type}")

                self._pool_stats['total_queries'] += len(params_list)
                batch_time = time.time() - start_time
                logger.debug(f"批量执行完成 ({batch_time:.3f}s): {len(params_list)} 条记录")

        except Exception as e:
            logger.error(f"批量执行失败: {e}, 记录数: {len(params_list)}")
            raise

    async def _health_check_loop(self) -> None:
        """连接池健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self._health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"健康检查出错: {e}")

    async def _perform_health_check(self) -> None:
        """执行健康检查"""
        try:
            async with self.get_connection() as conn:
                if self.config.db_type == 'mysql':
                    async with conn.cursor() as cursor:
                        await cursor.execute("SELECT 1")
                elif self.config.db_type == 'postgresql':
                    await conn.fetchval("SELECT 1")

            logger.debug("连接池健康检查通过")
        except Exception as e:
            logger.warning(f"连接池健康检查失败: {e}")

    def get_pool_stats(self) -> Dict[str, int]:
        """获取连接池统计信息"""
        return self._pool_stats.copy()
    
    async def get_table_list(self) -> List[str]:
        """
        获取表列表，支持不同数据库类型

        Returns:
            表名列表
        """
        try:
            if self.config.db_type == 'mysql':
                query = "SHOW TABLES"
                result = await self.execute_query(query)
                return [row[0] for row in result]
            elif self.config.db_type == 'postgresql':
                query = """
                    SELECT tablename FROM pg_tables
                    WHERE schemaname = 'public'
                    ORDER BY tablename
                """
                result = await self.execute_query(query)
                return [row['tablename'] for row in result]
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")
        except Exception as e:
            logger.error(f"获取表列表失败: {e}")
            raise

    async def get_row_count(self, table_name: str) -> int:
        """
        获取表行数，支持大表优化

        Args:
            table_name: 表名

        Returns:
            行数
        """
        try:
            # 对于大表，使用近似计数提高性能
            if self.config.db_type == 'mysql':
                # 先尝试从information_schema获取近似行数
                approx_query = """
                    SELECT table_rows FROM information_schema.tables
                    WHERE table_schema = DATABASE() AND table_name = %s
                """
                result = await self.execute_query(approx_query, (table_name,))
                if result and result[0][0] > 100000:  # 大表使用近似值
                    return result[0][0]

                # 小表使用精确计数
                query = f"SELECT COUNT(*) FROM `{table_name}`"
                result = await self.execute_query(query)
                return result[0][0]

            elif self.config.db_type == 'postgresql':
                # PostgreSQL使用统计信息
                approx_query = """
                    SELECT n_tup_ins - n_tup_del AS approx_count
                    FROM pg_stat_user_tables
                    WHERE relname = $1
                """
                result = await self.execute_query(approx_query, [table_name])
                if result and result[0]['approx_count'] > 100000:
                    return result[0]['approx_count']

                # 精确计数
                query = f'SELECT COUNT(*) FROM "{table_name}"'
                result = await self.execute_query(query)
                return result[0]['count']
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")

        except Exception as e:
            logger.warning(f"获取表 {table_name} 行数失败，使用默认值: {e}")
            return 0

    async def fetch_batch(self, table_name: str, columns: List[str],
                         offset: int, batch_size: int) -> List[Any]:
        """
        获取数据批次，支持内存优化

        Args:
            table_name: 表名
            columns: 列名列表
            offset: 偏移量
            batch_size: 批次大小

        Returns:
            数据批次
        """
        try:
            column_list = ', '.join(f'`{col}`' if self.config.db_type == 'mysql' else f'"{col}"'
                                  for col in columns)

            if self.config.db_type == 'mysql':
                query = f"SELECT {column_list} FROM `{table_name}` LIMIT {batch_size} OFFSET {offset}"
            elif self.config.db_type == 'postgresql':
                query = f'SELECT {column_list} FROM "{table_name}" LIMIT {batch_size} OFFSET {offset}'
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")

            return await self.execute_query(query)

        except Exception as e:
            logger.error(f"获取批次数据失败 (表: {table_name}, 偏移: {offset}): {e}")
            raise


class AsyncMigrationEngine:
    """
    高性能异步迁移引擎，优化版本包含：
    - 智能连接池管理
    - 内存优化的批处理
    - 并发控制和错误恢复
    - 性能监控和统计
    """

    def __init__(self, config: MigrationConfig) -> None:
        self.config = config
        self.source_db: Optional[AsyncDatabaseManager] = None
        self.target_db: Optional[AsyncDatabaseManager] = None
        self.stats = AsyncMigrationStats()
        self.progress_callback: Optional[Callable[[str, int, int], None]] = None
        self.executor = ThreadPoolExecutor(
            max_workers=min(config.parallel_workers, 8),  # 限制最大线程数
            thread_name_prefix="AsyncMigrator"
        )
        self._migration_semaphore: Optional[asyncio.Semaphore] = None
        self._batch_semaphore: Optional[asyncio.Semaphore] = None
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调"""
        self.progress_callback = callback
    
    async def connect_databases(self):
        """连接数据库"""
        logger.info("创建异步数据库连接...")
        
        # 只有MySQL和PostgreSQL支持异步
        if (self.config.source.db_type not in ['mysql', 'postgresql'] or 
            self.config.target.db_type not in ['mysql', 'postgresql']):
            raise ValueError("异步模式只支持MySQL和PostgreSQL")
        
        self.source_db = AsyncDatabaseManager(self.config.source)
        self.target_db = AsyncDatabaseManager(self.config.target)
        
        await self.source_db.create_pool()
        await self.target_db.create_pool()
        
        logger.info("异步数据库连接已建立")
    
    async def disconnect_databases(self):
        """断开数据库连接"""
        if self.source_db:
            await self.source_db.close_pool()
        if self.target_db:
            await self.target_db.close_pool()
        
        self.executor.shutdown(wait=True)
        logger.info("数据库连接已关闭")
    
    async def get_tables_to_migrate(self) -> List[str]:
        """获取要迁移的表列表"""
        all_tables = await self.source_db.get_table_list()
        
        if self.config.tables:
            tables = [t for t in self.config.tables if t in all_tables]
            missing = set(self.config.tables) - set(tables)
            if missing:
                logger.warning(f"源数据库中未找到表: {missing}")
        else:
            tables = all_tables
        
        if self.config.exclude_tables:
            tables = [t for t in tables if t not in self.config.exclude_tables]
        
        logger.info(f"将迁移 {len(tables)} 个表")
        return tables
    
    async def migrate_table_data_async(self, table_name: str, columns: List[str]) -> bool:
        """异步迁移表数据"""
        try:
            logger.info(f"开始异步迁移表数据: {table_name}")
            
            total_rows = await self.source_db.get_row_count(table_name)
            if total_rows == 0:
                logger.info(f"表 {table_name} 为空，跳过数据迁移")
                return True
            
            batch_size = self.config.batch_size
            migrated_rows = 0
            batch_times = []
            
            # 创建插入语句
            column_list = ', '.join(columns)
            placeholders = ', '.join(['%s'] * len(columns))
            insert_query = f"INSERT INTO {table_name} ({column_list}) VALUES ({placeholders})"
            
            # 并发处理批次
            semaphore = asyncio.Semaphore(self.config.parallel_workers)
            tasks = []
            
            for offset in range(0, total_rows, batch_size):
                task = self._process_batch_async(
                    semaphore, table_name, columns, offset, batch_size, 
                    insert_query, batch_times
                )
                tasks.append(task)
            
            # 等待所有批次完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"批次处理失败: {result}")
                    self.stats.errors += 1
                else:
                    migrated_rows += result
            
            # 更新统计信息
            self.stats.rows_migrated += migrated_rows
            if batch_times:
                self.stats.avg_batch_time = sum(batch_times) / len(batch_times)
            
            logger.info(f"表 {table_name} 数据迁移完成: {migrated_rows} 行")
            return True
            
        except Exception as e:
            logger.error(f"表 {table_name} 数据迁移失败: {str(e)}")
            self.stats.errors += 1
            return False
    
    async def _process_batch_async(self, semaphore: asyncio.Semaphore, 
                                  table_name: str, columns: List[str],
                                  offset: int, batch_size: int, 
                                  insert_query: str, batch_times: List[float]) -> int:
        """异步处理单个批次"""
        async with semaphore:
            start_time = time.time()
            
            try:
                # 获取数据
                batch_data = await self.source_db.fetch_batch(
                    table_name, columns, offset, batch_size
                )
                
                if not batch_data:
                    return 0
                
                # 插入数据
                await self.target_db.execute_many(insert_query, batch_data)
                
                batch_time = time.time() - start_time
                batch_times.append(batch_time)
                
                # 更新进度
                if self.progress_callback:
                    self.progress_callback(table_name, len(batch_data), offset + len(batch_data))
                
                return len(batch_data)
                
            except Exception as e:
                logger.error(f"批次处理失败 (offset={offset}): {str(e)}")
                raise
    
    async def migrate_table_async(self, table_name: str) -> bool:
        """异步迁移单个表"""
        success = True
        
        # 获取表结构（这部分仍使用同步方式）
        if not self.config.data_only:
            # 表结构迁移需要使用同步方式
            from .migrator import MigrationEngine
            sync_engine = MigrationEngine(self.config)
            sync_engine.connect_databases()
            success &= sync_engine.migrate_table_schema(table_name)
            sync_engine.disconnect_databases()
        
        # 异步迁移数据
        if not self.config.schema_only and success:
            # 获取列信息（简化版本）
            columns = ['*']  # 实际应用中需要获取具体列名
            success &= await self.migrate_table_data_async(table_name, columns)
        
        if success:
            self.stats.tables_migrated += 1
        
        return success
    
    async def migrate_async(self) -> AsyncMigrationStats:
        """执行异步迁移"""
        self.stats.start_time = time.time()
        
        try:
            logger.info("开始异步数据库迁移...")
            
            await self.connect_databases()
            
            tables = await self.get_tables_to_migrate()
            if not tables:
                logger.warning("没有要迁移的表")
                return self.stats
            
            # 并发迁移表
            tasks = [self.migrate_table_async(table) for table in tables]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"表迁移失败: {result}")
                    self.stats.errors += 1
            
            logger.info("异步迁移完成")
            
        except Exception as e:
            logger.error(f"异步迁移失败: {str(e)}")
            self.stats.errors += 1
        
        finally:
            await self.disconnect_databases()
            self.stats.end_time = time.time()
        
        return self.stats


# 使用示例
async def run_async_migration(config: MigrationConfig):
    """运行异步迁移"""
    engine = AsyncMigrationEngine(config)
    stats = await engine.migrate_async()
    return stats
