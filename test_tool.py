#!/usr/bin/env python3
"""
Simple test script for RDS Migration Tool
This script tests basic functionality without requiring actual database connections.
"""

import sys
import os
import tempfile
import yaml
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from rds_migrate.config import ConfigManager, DatabaseConfig, MigrationConfig
from rds_migrate.logger import MigrationLogger, ProgressTracker, MigrationReporter


def test_config_management():
    """Test configuration management functionality."""
    print("🧪 Testing Configuration Management...")
    
    try:
        # Create a temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config_path = f.name
        
        # Test creating sample config
        config_manager = ConfigManager()
        config_manager.create_sample_config(config_path)
        print("✅ Sample configuration created successfully")
        
        # Test loading config
        migration_config = config_manager.load_config(config_path)
        print("✅ Configuration loaded successfully")
        
        # Test validation
        config_manager.validate_config(migration_config)
        print("✅ Configuration validation passed")
        
        # Test config properties
        assert migration_config.source.db_type == "mysql"
        assert migration_config.target.db_type == "mysql"
        assert migration_config.batch_size == 1000
        print("✅ Configuration properties verified")
        
        # Clean up
        os.unlink(config_path)
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False
    
    return True


def test_logging_system():
    """Test logging and progress tracking."""
    print("\n🧪 Testing Logging System...")
    
    try:
        # Test logger initialization
        logger = MigrationLogger("INFO")
        log = logger.get_logger()
        print("✅ Logger initialized successfully")
        
        # Test progress tracker
        tracker = ProgressTracker()
        tracker.start_overall_progress(3)
        
        # Simulate table progress
        for i, table in enumerate(["users", "orders", "products"]):
            tracker.start_table_progress(table, 1000)
            for j in range(0, 1001, 100):
                tracker.update_table_progress(table, j, 1000)
            tracker.complete_table_progress(table, True)
        
        tracker.complete_overall_progress()
        print("✅ Progress tracking completed successfully")
        
        # Test reporter
        reporter = MigrationReporter(log)
        db_info = {
            'db_type': 'mysql',
            'host': 'localhost',
            'port': 3306,
            'database': 'test_db',
            'version': '8.0.0',
            'connected': True
        }
        
        # This will print to console - that's expected
        reporter.print_database_info(db_info, "Test")
        print("✅ Reporter functionality verified")
        
    except Exception as e:
        print(f"❌ Logging test failed: {str(e)}")
        return False
    
    return True


def test_cli_help():
    """Test CLI help functionality."""
    print("\n🧪 Testing CLI Help...")
    
    try:
        # Import CLI module
        from rds_migrate.cli import cli
        
        # Test that CLI can be imported without errors
        print("✅ CLI module imported successfully")
        
        # Note: We can't easily test click commands without mocking,
        # but importing successfully indicates basic functionality
        
    except Exception as e:
        print(f"❌ CLI test failed: {str(e)}")
        return False
    
    return True


def test_database_config_creation():
    """Test database configuration objects."""
    print("\n🧪 Testing Database Configuration...")
    
    try:
        # Test DatabaseConfig creation
        db_config = DatabaseConfig(
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password="test_password",
            db_type="mysql"
        )
        
        # Test serialization
        config_dict = db_config.to_dict()
        assert config_dict['host'] == "localhost"
        assert config_dict['db_type'] == "mysql"
        print("✅ DatabaseConfig creation and serialization successful")
        
        # Test MigrationConfig creation
        migration_config = MigrationConfig(
            source=db_config,
            target=db_config,
            batch_size=500,
            parallel_workers=2
        )
        
        migration_dict = migration_config.to_dict()
        assert migration_dict['batch_size'] == 500
        assert migration_dict['parallel_workers'] == 2
        print("✅ MigrationConfig creation and serialization successful")
        
    except Exception as e:
        print(f"❌ Database configuration test failed: {str(e)}")
        return False
    
    return True


def test_file_structure():
    """Test that all required files exist."""
    print("\n🧪 Testing File Structure...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "setup.py",
        "README.md",
        "USAGE_GUIDE.md",
        "rds_migrate/__init__.py",
        "rds_migrate/config.py",
        "rds_migrate/database.py",
        "rds_migrate/migrator.py",
        "rds_migrate/logger.py",
        "rds_migrate/cli.py",
        "examples/mysql_to_aws_rds.yaml",
        "examples/postgres_to_postgres.yaml",
        "examples/mysql_to_postgresql.yaml",
        "examples/schema_only_migration.yaml",
        "examples/data_only_migration.yaml"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required files present")
    return True


def test_example_configs():
    """Test that example configurations are valid."""
    print("\n🧪 Testing Example Configurations...")
    
    example_configs = [
        "examples/mysql_to_aws_rds.yaml",
        "examples/postgres_to_postgres.yaml",
        "examples/mysql_to_postgresql.yaml",
        "examples/schema_only_migration.yaml",
        "examples/data_only_migration.yaml"
    ]
    
    config_manager = ConfigManager()
    
    for config_path in example_configs:
        try:
            migration_config = config_manager.load_config(config_path)
            config_manager.validate_config(migration_config)
            print(f"✅ {config_path} is valid")
        except Exception as e:
            print(f"❌ {config_path} validation failed: {str(e)}")
            return False
    
    return True


def main():
    """Run all tests."""
    print("🚀 RDS Migration Tool - Basic Functionality Tests")
    print("=" * 60)
    
    tests = [
        test_file_structure,
        test_config_management,
        test_database_config_creation,
        test_logging_system,
        test_cli_help,
        test_example_configs
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The RDS Migration Tool is ready to use.")
        print("\n📝 Next steps:")
        print("1. Install database drivers: pip install pymysql psycopg2-binary pyodbc")
        print("2. Create configuration: python main.py init")
        print("3. Edit configuration with your database details")
        print("4. Test connections: python main.py test -c your_config.yaml")
        print("5. Run migration: python main.py migrate -c your_config.yaml")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
