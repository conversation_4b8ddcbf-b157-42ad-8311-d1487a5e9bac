"""
Command line interface for RDS migration tool.
"""

import click
import os
import sys
from pathlib import Path
from typing import Optional
from tabulate import tabulate
from .config import Config<PERSON><PERSON><PERSON>, MigrationConfig, DatabaseConfig
from .database import DatabaseManager
from .migrator import MigrationEngine
from .logger import MigrationLogger, ProgressTracker, MigrationReporter


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """RDS Migration Tool - Migrate databases between traditional DB, cloud RDS, and different RDS providers."""
    pass


@cli.command()
@click.option('--output', '-o', default='sample_config.yaml', help='Output configuration file path')
def init(output):
    """Initialize a sample configuration file."""
    try:
        config_manager = ConfigManager()
        config_manager.create_sample_config(output)
        click.echo(f"✅ Sample configuration created: {output}")
        click.echo("📝 Please edit the configuration file with your database details before running migration.")
    except Exception as e:
        click.echo(f"❌ Error creating configuration: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--config', '-c', required=True, help='Configuration file path')
def validate(config):
    """Validate configuration file."""
    try:
        config_manager = ConfigManager()
        migration_config = config_manager.load_config(config)
        config_manager.validate_config(migration_config)
        click.echo("✅ Configuration is valid")
        
        # Display configuration summary
        click.echo("\n📋 Configuration Summary:")
        click.echo(f"Source: {migration_config.source.db_type} @ {migration_config.source.host}:{migration_config.source.port}")
        click.echo(f"Target: {migration_config.target.db_type} @ {migration_config.target.host}:{migration_config.target.port}")
        click.echo(f"Batch size: {migration_config.batch_size}")
        click.echo(f"Parallel workers: {migration_config.parallel_workers}")
        
    except Exception as e:
        click.echo(f"❌ Configuration validation failed: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--config', '-c', required=True, help='Configuration file path')
def test(config):
    """Test database connections."""
    try:
        config_manager = ConfigManager()
        migration_config = config_manager.load_config(config)
        
        click.echo("🔍 Testing database connections...")
        
        # Test source connection
        click.echo("\n📡 Testing source database connection...")
        source_db = DatabaseManager(migration_config.source)
        if source_db.test_connection():
            click.echo("✅ Source database connection successful")
            source_info = source_db.get_database_info()
            click.echo(f"   Type: {source_info['db_type']}")
            click.echo(f"   Version: {source_info['version']}")
        else:
            click.echo("❌ Source database connection failed")
            sys.exit(1)
        
        # Test target connection
        click.echo("\n📡 Testing target database connection...")
        target_db = DatabaseManager(migration_config.target)
        if target_db.test_connection():
            click.echo("✅ Target database connection successful")
            target_info = target_db.get_database_info()
            click.echo(f"   Type: {target_info['db_type']}")
            click.echo(f"   Version: {target_info['version']}")
        else:
            click.echo("❌ Target database connection failed")
            sys.exit(1)
        
        click.echo("\n🎉 All database connections are working!")
        
    except Exception as e:
        click.echo(f"❌ Connection test failed: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--config', '-c', required=True, help='Configuration file path')
@click.option('--table', '-t', help='Specific table to analyze (optional)')
def analyze(config, table):
    """Analyze source database structure."""
    try:
        config_manager = ConfigManager()
        migration_config = config_manager.load_config(config)
        
        source_db = DatabaseManager(migration_config.source)
        source_db.connect()
        
        if table:
            # Analyze specific table
            click.echo(f"📊 Analyzing table: {table}")
            schema = source_db.get_table_schema(table)
            row_count = source_db.get_row_count(table)
            
            click.echo(f"\nTable: {table}")
            click.echo(f"Rows: {row_count:,}")
            click.echo(f"Columns: {len(schema['columns'])}")
            
            # Display column information
            column_data = []
            for col in schema['columns']:
                column_data.append([
                    col['name'],
                    col['type'],
                    'YES' if col['nullable'] else 'NO',
                    'YES' if col['primary_key'] else 'NO',
                    col['default'] or ''
                ])
            
            click.echo("\nColumns:")
            click.echo(tabulate(column_data, 
                              headers=['Name', 'Type', 'Nullable', 'Primary Key', 'Default'],
                              tablefmt='grid'))
        else:
            # Analyze all tables
            click.echo("📊 Analyzing source database...")
            tables = source_db.get_table_list()
            
            table_data = []
            total_rows = 0
            
            for table_name in tables:
                try:
                    row_count = source_db.get_row_count(table_name)
                    schema = source_db.get_table_schema(table_name)
                    table_data.append([
                        table_name,
                        f"{row_count:,}",
                        len(schema['columns']),
                        len(schema['indexes'])
                    ])
                    total_rows += row_count
                except Exception as e:
                    table_data.append([table_name, 'Error', 'Error', 'Error'])
            
            click.echo(f"\nDatabase Analysis Summary:")
            click.echo(f"Total tables: {len(tables)}")
            click.echo(f"Total rows: {total_rows:,}")
            
            click.echo("\nTable Details:")
            click.echo(tabulate(table_data,
                              headers=['Table', 'Rows', 'Columns', 'Indexes'],
                              tablefmt='grid'))
        
        source_db.disconnect()
        
    except Exception as e:
        click.echo(f"❌ Analysis failed: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--config', '-c', required=True, help='Configuration file path')
@click.option('--log-level', default='INFO', help='Log level (DEBUG, INFO, WARNING, ERROR)')
@click.option('--log-file', help='Log file path (optional)')
@click.option('--dry-run', is_flag=True, help='Perform a dry run without actual migration')
@click.option('--report', help='Save migration report to file')
def migrate(config, log_level, log_file, dry_run, report):
    """Execute database migration."""
    try:
        # Setup logging
        migration_logger = MigrationLogger(log_level, log_file)
        logger = migration_logger.get_logger()
        
        if dry_run:
            logger.info("🧪 DRY RUN MODE - No actual migration will be performed")
        
        # Load configuration
        config_manager = ConfigManager()
        migration_config = config_manager.load_config(config)
        config_manager.validate_config(migration_config)
        
        # Initialize components
        migration_engine = MigrationEngine(migration_config)
        progress_tracker = ProgressTracker()
        reporter = MigrationReporter(logger)
        
        # Set up progress callback
        migration_engine.set_progress_callback(progress_tracker.update_table_progress)
        
        logger.info("🚀 Starting RDS Migration")
        
        # Connect and get database info
        migration_engine.connect_databases()
        
        source_info = migration_engine.source_db.get_database_info()
        target_info = migration_engine.target_db.get_database_info()
        
        reporter.print_database_info(source_info, "Source")
        reporter.print_database_info(target_info, "Target")
        
        # Get tables to migrate
        tables = migration_engine.get_tables_to_migrate()
        logger.info(f"📋 Tables to migrate: {len(tables)}")
        
        if not tables:
            logger.warning("No tables to migrate")
            return
        
        # Start progress tracking
        progress_tracker.start_overall_progress(len(tables))
        
        if dry_run:
            # Simulate migration for dry run
            for table in tables:
                logger.info(f"[DRY RUN] Would migrate table: {table}")
                row_count = migration_engine.source_db.get_row_count(table)
                progress_tracker.start_table_progress(table, row_count)
                progress_tracker.complete_table_progress(table, True)
            
            logger.info("🧪 Dry run completed successfully")
        else:
            # Execute actual migration
            stats = migration_engine.migrate()
            
            # Print summary
            reporter.print_migration_summary(stats, progress_tracker)
            
            # Save report if requested
            if report:
                reporter.save_migration_report(stats, progress_tracker, report)
        
        progress_tracker.complete_overall_progress()
        
    except Exception as e:
        click.echo(f"❌ Migration failed: {str(e)}", err=True)
        sys.exit(1)


@cli.command()
def interactive():
    """Interactive configuration wizard."""
    click.echo("🧙 RDS Migration Configuration Wizard")
    click.echo("=====================================")
    
    try:
        # Source database configuration
        click.echo("\n📥 Source Database Configuration:")
        source_config = _interactive_db_config("source")
        
        # Target database configuration
        click.echo("\n📤 Target Database Configuration:")
        target_config = _interactive_db_config("target")
        
        # Migration options
        click.echo("\n⚙️  Migration Options:")
        tables = click.prompt("Tables to migrate (comma-separated, leave empty for all)", default="", show_default=False)
        exclude_tables = click.prompt("Tables to exclude (comma-separated)", default="", show_default=False)
        batch_size = click.prompt("Batch size", default=1000, type=int)
        parallel_workers = click.prompt("Parallel workers", default=4, type=int)
        create_indexes = click.confirm("Create indexes?", default=True)
        create_foreign_keys = click.confirm("Create foreign keys?", default=True)
        
        # Create configuration
        migration_config = MigrationConfig(
            source=source_config,
            target=target_config,
            tables=tables.split(',') if tables.strip() else None,
            exclude_tables=exclude_tables.split(',') if exclude_tables.strip() else None,
            batch_size=batch_size,
            parallel_workers=parallel_workers,
            create_indexes=create_indexes,
            create_foreign_keys=create_foreign_keys
        )
        
        # Save configuration
        output_file = click.prompt("Configuration file name", default="migration_config.yaml")
        config_manager = ConfigManager()
        config_manager.save_config(migration_config, output_file)
        
        click.echo(f"\n✅ Configuration saved to: {output_file}")
        click.echo("🚀 You can now run: rds-migrate migrate -c " + output_file)
        
    except Exception as e:
        click.echo(f"❌ Configuration wizard failed: {str(e)}", err=True)
        sys.exit(1)


def _interactive_db_config(db_type: str) -> DatabaseConfig:
    """Interactive database configuration."""
    db_types = ['mysql', 'postgresql', 'sqlserver', 'oracle']
    
    db_engine = click.prompt(f"{db_type.title()} database type", 
                           type=click.Choice(db_types), default='mysql')
    host = click.prompt(f"{db_type.title()} host")
    port = click.prompt(f"{db_type.title()} port", type=int, 
                       default=3306 if db_engine == 'mysql' else 5432)
    database = click.prompt(f"{db_type.title()} database name")
    username = click.prompt(f"{db_type.title()} username")
    password = click.prompt(f"{db_type.title()} password", hide_input=True)
    
    return DatabaseConfig(
        host=host,
        port=port,
        database=database,
        username=username,
        password=password,
        db_type=db_engine
    )


def main():
    """CLI主入口函数"""
    cli()

if __name__ == '__main__':
    main()
