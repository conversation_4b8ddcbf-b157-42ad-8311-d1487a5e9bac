"""
Configuration management for RDS migration tool.
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class DatabaseConfig:
    """Database connection configuration."""
    host: str
    port: int
    database: str
    username: str
    password: str
    db_type: str  # mysql, postgresql, sqlserver, oracle
    ssl_mode: Optional[str] = None
    charset: Optional[str] = 'utf8mb4'
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class MigrationConfig:
    """Migration task configuration."""
    source: DatabaseConfig
    target: DatabaseConfig
    tables: Optional[list] = None  # None means all tables
    exclude_tables: Optional[list] = None
    batch_size: int = 1000
    parallel_workers: int = 4
    create_indexes: bool = True
    create_foreign_keys: bool = True
    data_only: bool = False
    schema_only: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class ConfigManager:
    """Manages configuration files and validation."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "migration_config.yaml"
        
    def load_config(self, config_path: Optional[str] = None) -> MigrationConfig:
        """Load configuration from YAML file."""
        path = config_path or self.config_path
        
        if not os.path.exists(path):
            raise FileNotFoundError(f"Configuration file not found: {path}")
            
        with open(path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
            
        return self._parse_config(config_data)
    
    def save_config(self, config: MigrationConfig, config_path: Optional[str] = None):
        """Save configuration to YAML file."""
        path = config_path or self.config_path
        
        # Ensure directory exists
        Path(path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(config.to_dict(), f, default_flow_style=False, indent=2)
    
    def _parse_config(self, config_data: Dict[str, Any]) -> MigrationConfig:
        """Parse configuration data into MigrationConfig object."""
        source_data = config_data['source']
        target_data = config_data['target']
        
        source = DatabaseConfig(**source_data)
        target = DatabaseConfig(**target_data)
        
        migration_data = config_data.get('migration', {})
        
        return MigrationConfig(
            source=source,
            target=target,
            tables=migration_data.get('tables'),
            exclude_tables=migration_data.get('exclude_tables'),
            batch_size=migration_data.get('batch_size', 1000),
            parallel_workers=migration_data.get('parallel_workers', 4),
            create_indexes=migration_data.get('create_indexes', True),
            create_foreign_keys=migration_data.get('create_foreign_keys', True),
            data_only=migration_data.get('data_only', False),
            schema_only=migration_data.get('schema_only', False)
        )
    
    def validate_config(self, config: MigrationConfig) -> bool:
        """Validate configuration parameters."""
        errors = []
        
        # Validate database types
        supported_types = ['mysql', 'postgresql', 'sqlserver', 'oracle']
        if config.source.db_type not in supported_types:
            errors.append(f"Unsupported source database type: {config.source.db_type}")
        if config.target.db_type not in supported_types:
            errors.append(f"Unsupported target database type: {config.target.db_type}")
            
        # Validate batch size
        if config.batch_size <= 0:
            errors.append("Batch size must be greater than 0")
            
        # Validate parallel workers
        if config.parallel_workers <= 0:
            errors.append("Parallel workers must be greater than 0")
            
        # Validate conflicting options
        if config.data_only and config.schema_only:
            errors.append("Cannot specify both data_only and schema_only")
            
        if errors:
            raise ValueError("Configuration validation failed:\n" + "\n".join(errors))
            
        return True
    
    def create_sample_config(self, output_path: str = "sample_config.yaml"):
        """Create a sample configuration file."""
        sample_config = MigrationConfig(
            source=DatabaseConfig(
                host="source-db.example.com",
                port=3306,
                database="source_db",
                username="source_user",
                password="source_password",
                db_type="mysql"
            ),
            target=DatabaseConfig(
                host="target-rds.amazonaws.com",
                port=3306,
                database="target_db", 
                username="target_user",
                password="target_password",
                db_type="mysql"
            ),
            tables=["users", "orders", "products"],
            exclude_tables=["temp_table", "log_table"],
            batch_size=1000,
            parallel_workers=4
        )
        
        self.save_config(sample_config, output_path)
        print(f"Sample configuration created: {output_path}")
