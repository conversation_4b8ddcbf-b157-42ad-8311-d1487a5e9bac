# RDS Migration Tool - Docker Compose 配置
# 企业级数据库迁移工具完整部署方案

version: '3.8'

services:
  # 主应用服务
  rds-migrate:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rds-migrate-app
    restart: unless-stopped
    ports:
      - "8000:8000"  # Web界面
      - "8080:8080"  # API接口
    environment:
      - PYTHONUNBUFFERED=1
      - RDS_MIGRATE_ENV=production
      - RDS_MIGRATE_CONFIG_DIR=/app/config
      - RDS_MIGRATE_DATA_DIR=/app/data
      - RDS_MIGRATE_LOG_DIR=/app/logs
      - REDIS_URL=redis://redis:6379/0
      - POSTGRES_URL=********************************************/rds_migrate
    volumes:
      - ./config:/app/config:ro
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - redis
      - postgres
    networks:
      - rds-migrate-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: rds-migrate-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    networks:
      - rds-migrate-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL元数据库
  postgres:
    image: postgres:15-alpine
    container_name: rds-migrate-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=rds_migrate
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - rds-migrate-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: rds-migrate-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - rds-migrate
    networks:
      - rds-migrate-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: rds-migrate-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - rds-migrate-network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: rds-migrate-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      - prometheus
    networks:
      - rds-migrate-network

  # 日志收集器
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: rds-migrate-fluentd
    restart: unless-stopped
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./logging/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/rds-migrate
    networks:
      - rds-migrate-network

# 网络配置
networks:
  rds-migrate-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
