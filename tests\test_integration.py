"""
集成测试
"""

import pytest
import tempfile
import os
import sqlite3
import json
from pathlib import Path
from unittest.mock import patch

from rds_migrate.config import ConfigManager, MigrationConfig
from rds_migrate.database import DatabaseManager
from rds_migrate.migrator import MigrationEngine


@pytest.mark.integration
class TestIntegration:
    """集成测试"""
    
    @pytest.fixture
    def sqlite_source_db(self):
        """创建SQLite源数据库"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建测试表
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE orders (
                id INTEGER PRIMARY KEY,
                user_id INTEGER,
                amount DECIMAL(10,2),
                status TEXT DEFAULT 'pending',
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # 插入测试数据
        users_data = [
            (1, 'Alice', '<EMAIL>'),
            (2, 'Bob', '<EMAIL>'),
            (3, 'Charlie', '<EMAIL>')
        ]
        cursor.executemany('INSERT INTO users (id, name, email) VALUES (?, ?, ?)', users_data)
        
        orders_data = [
            (1, 1, 99.99, 'completed'),
            (2, 1, 149.99, 'pending'),
            (3, 2, 79.99, 'completed'),
            (4, 3, 199.99, 'pending')
        ]
        cursor.executemany('INSERT INTO orders (id, user_id, amount, status) VALUES (?, ?, ?, ?)', orders_data)
        
        conn.commit()
        conn.close()
        
        yield db_path
        
        # 清理
        os.unlink(db_path)
    
    @pytest.fixture
    def sqlite_target_db(self):
        """创建SQLite目标数据库"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        yield db_path
        
        # 清理
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    @pytest.fixture
    def integration_config(self, sqlite_source_db, sqlite_target_db):
        """集成测试配置"""
        config_dict = {
            "source": {
                "db_type": "sqlite",
                "database": sqlite_source_db,
                "host": "",
                "port": 0,
                "username": "",
                "password": ""
            },
            "target": {
                "db_type": "sqlite",
                "database": sqlite_target_db,
                "host": "",
                "port": 0,
                "username": "",
                "password": ""
            },
            "batch_size": 100,
            "parallel_workers": 1,
            "tables": ["users", "orders"],
            "exclude_tables": [],
            "schema_only": False,
            "data_only": False,
            "create_indexes": True,
            "create_foreign_keys": True
        }
        
        return MigrationConfig.from_dict(config_dict)
    
    def test_full_migration_workflow(self, integration_config):
        """测试完整迁移工作流"""
        # 注意：这个测试需要实际的数据库连接，在CI环境中可能需要跳过
        pytest.skip("需要实际数据库连接，在模拟环境中跳过")
        
        engine = MigrationEngine(integration_config)
        
        # 连接数据库
        assert engine.connect_databases() == True
        
        try:
            # 获取要迁移的表
            tables = engine.get_tables_to_migrate()
            assert "users" in tables
            assert "orders" in tables
            
            # 迁移表结构
            for table in tables:
                assert engine.migrate_table_schema(table) == True
            
            # 迁移数据
            for table in tables:
                assert engine.migrate_table_data(table) == True
            
            # 验证迁移结果
            assert engine.validate_migration(tables) == True
            
        finally:
            engine.cleanup()
    
    def test_config_file_integration(self, temp_dir, integration_config):
        """测试配置文件集成"""
        config_manager = ConfigManager()
        
        # 创建配置文件
        config_file = temp_dir / "integration_config.yaml"
        config_dict = {
            "source": {
                "db_type": "mysql",
                "host": "localhost",
                "port": 3306,
                "database": "test_source",
                "username": "test",
                "password": "test"
            },
            "target": {
                "db_type": "mysql",
                "host": "localhost",
                "port": 3306,
                "database": "test_target",
                "username": "test",
                "password": "test"
            },
            "batch_size": 1000,
            "parallel_workers": 2,
            "tables": ["users", "orders"],
            "exclude_tables": ["temp_table"]
        }
        
        # 保存配置
        import yaml
        with open(config_file, 'w') as f:
            yaml.dump(config_dict, f)
        
        # 加载配置
        loaded_config = config_manager.load_config(str(config_file))
        
        # 验证配置
        assert isinstance(loaded_config, MigrationConfig)
        assert loaded_config.source.db_type == "mysql"
        assert loaded_config.target.db_type == "mysql"
        assert loaded_config.batch_size == 1000
        assert loaded_config.parallel_workers == 2
        assert loaded_config.tables == ["users", "orders"]
        assert loaded_config.exclude_tables == ["temp_table"]
    
    def test_cli_integration(self, temp_dir):
        """测试CLI集成"""
        from click.testing import CliRunner
        from rds_migrate.cli import main, init, validate
        
        runner = CliRunner()
        
        # 测试init命令
        config_file = temp_dir / "cli_test_config.yaml"
        result = runner.invoke(init, [str(config_file)])
        
        assert result.exit_code == 0
        assert config_file.exists()
        
        # 测试validate命令
        result = runner.invoke(validate, [str(config_file)])
        
        assert result.exit_code == 0
        assert "Configuration is valid" in result.output
    
    def test_error_handling_integration(self, integration_config):
        """测试错误处理集成"""
        # 测试无效的源数据库配置
        integration_config.source.host = "invalid_host"
        integration_config.source.port = 99999
        
        engine = MigrationEngine(integration_config)
        
        # 连接数据库应该失败
        assert engine.connect_databases() == False
    
    def test_progress_callback_integration(self, integration_config):
        """测试进度回调集成"""
        progress_updates = []
        
        def progress_callback(current, total, message):
            progress_updates.append((current, total, message))
        
        engine = MigrationEngine(integration_config)
        engine.progress_callback = progress_callback
        
        # 模拟迁移过程
        with patch.object(engine, 'connect_databases', return_value=True), \
             patch.object(engine, 'source_db') as mock_source, \
             patch.object(engine, 'target_db') as mock_target:
            
            mock_source.get_table_count.return_value = 1000
            mock_source.execute_query.side_effect = [
                [{"id": i} for i in range(500)],  # 第一批
                [{"id": i} for i in range(500, 1000)],  # 第二批
                []  # 结束
            ]
            
            result = engine.migrate_table_data("users")
            
            assert result == True
            assert len(progress_updates) > 0
            
            # 验证进度更新
            for current, total, message in progress_updates:
                assert isinstance(current, int)
                assert isinstance(total, int)
                assert isinstance(message, str)
                assert current <= total
    
    def test_batch_processing_integration(self, integration_config):
        """测试批处理集成"""
        integration_config.batch_size = 2  # 小批次用于测试
        
        engine = MigrationEngine(integration_config)
        
        with patch.object(engine, 'connect_databases', return_value=True), \
             patch.object(engine, 'source_db') as mock_source, \
             patch.object(engine, 'target_db') as mock_target:
            
            # 模拟5条记录，批次大小为2
            mock_source.get_table_count.return_value = 5
            mock_source.execute_query.side_effect = [
                [{"id": 1}, {"id": 2}],  # 第一批
                [{"id": 3}, {"id": 4}],  # 第二批
                [{"id": 5}],             # 第三批
                []                       # 结束
            ]
            
            result = engine.migrate_table_data("users")
            
            assert result == True
            
            # 验证批次调用
            assert mock_source.execute_query.call_count == 4  # 3批数据 + 1次结束检查
            assert mock_target.insert_data.call_count == 3   # 3批插入
    
    def test_table_filtering_integration(self, integration_config):
        """测试表过滤集成"""
        engine = MigrationEngine(integration_config)
        
        with patch.object(engine, 'source_db') as mock_source:
            # 模拟数据库中的所有表
            mock_source.get_table_list.return_value = ["users", "orders", "products", "temp_table", "log_table"]
            
            # 测试指定表列表
            integration_config.tables = ["users", "orders"]
            tables = engine.get_tables_to_migrate()
            assert set(tables) == {"users", "orders"}
            
            # 测试排除表
            integration_config.tables = []  # 空列表表示所有表
            integration_config.exclude_tables = ["temp_table", "log_table"]
            tables = engine.get_tables_to_migrate()
            assert set(tables) == {"users", "orders", "products"}
    
    def test_schema_only_migration_integration(self, integration_config):
        """测试仅结构迁移集成"""
        integration_config.schema_only = True
        
        engine = MigrationEngine(integration_config)
        
        with patch.object(engine, 'connect_databases', return_value=True), \
             patch.object(engine, 'get_tables_to_migrate', return_value=["users"]), \
             patch.object(engine, 'migrate_table_schema', return_value=True) as mock_schema, \
             patch.object(engine, 'migrate_table_data', return_value=True) as mock_data, \
             patch.object(engine, 'disconnect_databases'):
            
            result = engine.migrate()
            
            assert result == True
            mock_schema.assert_called_once_with("users")
            mock_data.assert_not_called()  # 不应该迁移数据
    
    def test_data_only_migration_integration(self, integration_config):
        """测试仅数据迁移集成"""
        integration_config.data_only = True
        
        engine = MigrationEngine(integration_config)
        
        with patch.object(engine, 'connect_databases', return_value=True), \
             patch.object(engine, 'get_tables_to_migrate', return_value=["users"]), \
             patch.object(engine, 'migrate_table_schema', return_value=True) as mock_schema, \
             patch.object(engine, 'migrate_table_data', return_value=True) as mock_data, \
             patch.object(engine, 'disconnect_databases'):
            
            result = engine.migrate()
            
            assert result == True
            mock_schema.assert_not_called()  # 不应该迁移结构
            mock_data.assert_called_once_with("users")
