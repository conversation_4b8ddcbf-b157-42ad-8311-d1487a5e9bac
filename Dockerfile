# RDS Migration Tool - Multi-stage Docker Build
# 企业级数据库迁移工具容器化部署

# 第一阶段：构建阶段
FROM python:3.11-slim as builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 创建虚拟环境并安装依赖
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# 第二阶段：运行阶段
FROM python:3.11-slim as runtime

# 设置标签
LABEL maintainer="RDS Migration Team"
LABEL version="2.0.0"
LABEL description="Enterprise-grade RDS Migration Tool"

# 创建非root用户
RUN groupadd -r rdsuser && useradd -r -g rdsuser rdsuser

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libpq5 \
    default-mysql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/logs /app/data /app/config /app/backups

# 设置权限
RUN chown -R rdsuser:rdsuser /app
RUN chmod +x /app/scripts/*.sh 2>/dev/null || true

# 切换到非root用户
USER rdsuser

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV RDS_MIGRATE_CONFIG_DIR=/app/config
ENV RDS_MIGRATE_DATA_DIR=/app/data
ENV RDS_MIGRATE_LOG_DIR=/app/logs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# 暴露端口
EXPOSE 8000 8080

# 默认启动命令
CMD ["python", "-m", "rds_migrate.web_interface"]
