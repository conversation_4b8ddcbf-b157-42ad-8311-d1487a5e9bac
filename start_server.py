#!/usr/bin/env python3
"""
RDS迁移工具启动脚本
自动检测环境并选择最佳的启动方式
"""

import sys
import os
import logging
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    logger.info(f"✓ Python版本: {sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'jinja2',
        'pydantic'
    ]
    
    missing_packages = []
    available_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            available_packages.append(package)
            logger.info(f"✓ {package} 可用")
        except ImportError:
            missing_packages.append(package)
            logger.warning(f"✗ {package} 不可用")
    
    return available_packages, missing_packages

def install_dependencies():
    """安装缺失的依赖"""
    logger.info("正在安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        logger.info("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ 依赖包安装失败: {e}")
        return False
    except FileNotFoundError:
        logger.error("✗ requirements.txt 文件不存在")
        return False

def start_web_server():
    """启动Web服务器"""
    logger.info("正在启动Web服务器...")
    
    # 检查是否有安全版服务器
    safe_server_path = Path("rds_migrate/safe_web_server.py")
    enhanced_server_path = Path("rds_migrate/enhanced_web_server.py")
    
    try:
        if safe_server_path.exists():
            logger.info("使用安全版Web服务器")
            from rds_migrate.safe_web_server import SafeWebServer
            server = SafeWebServer()
            server.run(host="0.0.0.0", port=8000)
        elif enhanced_server_path.exists():
            logger.info("使用增强版Web服务器")
            from rds_migrate.enhanced_web_server import EnhancedWebServer
            server = EnhancedWebServer()
            server.run(host="0.0.0.0", port=8000)
        else:
            logger.error("找不到Web服务器文件")
            return False
    except Exception as e:
        logger.error(f"启动Web服务器失败: {e}")
        return False
    
    return True

def start_cli():
    """启动命令行界面"""
    logger.info("启动命令行界面...")
    try:
        from rds_migrate.cli import main
        main()
    except Exception as e:
        logger.error(f"启动CLI失败: {e}")
        return False
    return True

def start_gui():
    """启动图形界面"""
    logger.info("启动图形界面...")
    try:
        from rds_migrate.gui.main_window import main
        main()
    except Exception as e:
        logger.error(f"启动GUI失败: {e}")
        return False
    return True

def show_menu():
    """显示启动菜单"""
    print("\n" + "="*50)
    print("🚀 RDS迁移工具 - 企业级数据库迁移解决方案")
    print("="*50)
    print("请选择启动模式:")
    print("1. Web界面 (推荐)")
    print("2. 命令行界面")
    print("3. 图形界面")
    print("4. 安装/更新依赖")
    print("5. 退出")
    print("="*50)

def main():
    """主函数"""
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖
    available, missing = check_dependencies()
    
    if missing:
        print(f"\n⚠️  缺少以下依赖包: {', '.join(missing)}")
        print("建议先安装依赖包以获得最佳体验")
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请输入选择 (1-5): ").strip()
            
            if choice == "1":
                # Web界面
                if not start_web_server():
                    print("Web服务器启动失败，请检查依赖包是否完整安装")
                    continue
                break
                
            elif choice == "2":
                # 命令行界面
                if not start_cli():
                    print("CLI启动失败")
                    continue
                break
                
            elif choice == "3":
                # 图形界面
                if not start_gui():
                    print("GUI启动失败，可能缺少tkinter支持")
                    continue
                break
                
            elif choice == "4":
                # 安装依赖
                if install_dependencies():
                    print("依赖包安装完成，请重新选择启动模式")
                    available, missing = check_dependencies()
                else:
                    print("依赖包安装失败")
                continue
                
            elif choice == "5":
                # 退出
                print("感谢使用RDS迁移工具！")
                break
                
            else:
                print("无效选择，请重新输入")
                continue
                
        except KeyboardInterrupt:
            print("\n\n感谢使用RDS迁移工具！")
            break
        except Exception as e:
            logger.error(f"发生错误: {e}")
            continue

if __name__ == "__main__":
    main()
