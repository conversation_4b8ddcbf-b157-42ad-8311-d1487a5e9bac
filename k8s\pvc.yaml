# Kubernetes PersistentVolumeClaim配置
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rds-migrate-data-pvc
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: data
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rds-migrate-logs-pvc
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: logs
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rds-migrate-backups-pvc
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: backups
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-data-pvc
  namespace: rds-migrate
  labels:
    app: postgres
    component: data
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 20Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data-pvc
  namespace: rds-migrate
  labels:
    app: redis
    component: data
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 5Gi
