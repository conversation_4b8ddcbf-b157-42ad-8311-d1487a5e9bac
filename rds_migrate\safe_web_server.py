#!/usr/bin/env python3
"""
安全版Web服务器 - 带有完整错误处理和回退机制的RDS迁移工具Web界面
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SafeWebServer:
    """安全版Web服务器 - 带有完整的依赖检查和错误处理"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.app = None
        self.websocket_connections: List = []
        self.templates = None
        
        # 检查并导入依赖
        self.check_dependencies()
        
        # 初始化FastAPI应用
        self.init_app()
        
        # 初始化组件
        self.init_components()
        
        # 设置路由
        self.setup_routes()
    
    def check_dependencies(self):
        """检查必要的依赖"""
        self.dependencies = {
            'fastapi': False,
            'uvicorn': False,
            'jinja2': False,
            'websockets': False,
            'pydantic': False
        }
        
        # 检查FastAPI
        try:
            import fastapi
            self.dependencies['fastapi'] = True
            logger.info("✓ FastAPI 可用")
        except ImportError:
            logger.warning("✗ FastAPI 不可用，将使用简单HTTP服务器")
        
        # 检查Uvicorn
        try:
            import uvicorn
            self.dependencies['uvicorn'] = True
            logger.info("✓ Uvicorn 可用")
        except ImportError:
            logger.warning("✗ Uvicorn 不可用")
        
        # 检查Jinja2
        try:
            import jinja2
            self.dependencies['jinja2'] = True
            logger.info("✓ Jinja2 可用")
        except ImportError:
            logger.warning("✗ Jinja2 不可用，将使用静态HTML")
        
        # 检查WebSockets
        try:
            import websockets
            self.dependencies['websockets'] = True
            logger.info("✓ WebSockets 可用")
        except ImportError:
            logger.warning("✗ WebSockets 不可用，将禁用实时功能")
        
        # 检查Pydantic
        try:
            import pydantic
            self.dependencies['pydantic'] = True
            logger.info("✓ Pydantic 可用")
        except ImportError:
            logger.warning("✗ Pydantic 不可用")
    
    def init_app(self):
        """初始化应用"""
        if self.dependencies['fastapi']:
            from fastapi import FastAPI
            from fastapi.staticfiles import StaticFiles
            from fastapi.middleware.cors import CORSMiddleware
            
            self.app = FastAPI(
                title="RDS Migration Tool",
                description="数据库迁移工具",
                version="2.0.0"
            )
            
            # 添加CORS中间件
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            # 静态文件
            static_dir = os.path.join(os.path.dirname(__file__), "static")
            if os.path.exists(static_dir):
                self.app.mount("/static", StaticFiles(directory=static_dir), name="static")
            
            logger.info("✓ FastAPI应用初始化完成")
        else:
            logger.warning("✗ 无法初始化FastAPI应用，将使用简单服务器")
    
    def init_components(self):
        """初始化组件"""
        # 尝试初始化性能监控
        try:
            from .performance_monitor import PerformanceMonitor
            self.performance_monitor = PerformanceMonitor()
            logger.info("✓ 性能监控组件加载成功")
        except ImportError:
            self.performance_monitor = None
            logger.warning("✗ 性能监控组件不可用")
        
        # 尝试初始化数据验证器
        try:
            from .data_validator import DataValidator
            self.data_validator = DataValidator()
            logger.info("✓ 数据验证组件加载成功")
        except ImportError:
            self.data_validator = None
            logger.warning("✗ 数据验证组件不可用")
        
        # 尝试初始化安全管理器
        try:
            from .security_manager import SecurityManager
            self.security_manager = SecurityManager()
            logger.info("✓ 安全管理组件加载成功")
        except ImportError:
            self.security_manager = None
            logger.warning("✗ 安全管理组件不可用")
        
        # 尝试初始化迁移引擎
        try:
            from .enhanced_async_migrator import EnhancedAsyncMigrator
            self.migration_engine = EnhancedAsyncMigrator()
            logger.info("✓ 增强迁移引擎加载成功")
        except ImportError:
            try:
                from .async_migrator import AsyncMigrator
                self.migration_engine = AsyncMigrator()
                logger.info("✓ 异步迁移引擎加载成功")
            except ImportError:
                self.migration_engine = None
                logger.warning("✗ 迁移引擎不可用")
        
        # 初始化模板引擎
        if self.dependencies['jinja2']:
            from fastapi.templating import Jinja2Templates
            templates_dir = os.path.join(os.path.dirname(__file__), "templates")
            if os.path.exists(templates_dir):
                self.templates = Jinja2Templates(directory=templates_dir)
                logger.info("✓ 模板引擎初始化完成")
            else:
                logger.warning("✗ 模板目录不存在")
        else:
            self.templates = None
            logger.warning("✗ 模板引擎不可用")
    
    def setup_routes(self):
        """设置路由"""
        if not self.app:
            return
        
        from fastapi import Request, HTTPException
        from fastapi.responses import HTMLResponse, JSONResponse
        
        # 主页路由
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard(request: Request):
            if self.templates:
                try:
                    return self.templates.TemplateResponse("dashboard.html", {"request": request})
                except Exception as e:
                    logger.error(f"模板渲染失败: {e}")
                    return HTMLResponse(self.get_fallback_html("仪表板"))
            else:
                return HTMLResponse(self.get_fallback_html("仪表板"))
        
        # API路由
        @self.app.get("/api/dashboard/stats")
        async def get_dashboard_stats():
            """获取仪表板统计数据"""
            return JSONResponse(content={
                "metrics": {
                    "active_migrations": 0,
                    "completed_migrations": 0,
                    "total_tables": 0,
                    "data_transferred": 0
                },
                "activities": []
            })
        
        @self.app.get("/api/monitoring/system")
        async def get_system_metrics():
            """获取系统性能指标"""
            if self.performance_monitor:
                try:
                    metrics = await self.performance_monitor.get_current_metrics()
                    return JSONResponse(content=metrics)
                except Exception as e:
                    logger.error(f"获取性能指标失败: {e}")
            
            # 返回模拟数据
            return JSONResponse(content={
                "cpu": 0,
                "memory": 0,
                "network": 0,
                "connections": 0,
                "diskIO": 0,
                "connectionPool": {
                    "active": 0,
                    "idle": 0,
                    "available": 0
                }
            })
        
        # 健康检查
        @self.app.get("/health")
        async def health_check():
            """健康检查端点"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "dependencies": self.dependencies
            }
        
        logger.info("✓ 路由设置完成")
    
    def get_fallback_html(self, title: str) -> str:
        """获取回退HTML页面"""
        return f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>RDS迁移工具 - {title}</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                }}
                .status {{
                    padding: 15px;
                    margin: 10px 0;
                    border-radius: 4px;
                    border-left: 4px solid #007bff;
                    background-color: #f8f9fa;
                }}
                .warning {{
                    border-left-color: #ffc107;
                    background-color: #fff3cd;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 RDS迁移工具</h1>
                    <p>企业级数据库迁移解决方案</p>
                </div>
                
                <div class="status">
                    <h3>系统状态</h3>
                    <p>Web服务器正在运行，但某些功能可能不可用。</p>
                </div>
                
                <div class="status warning">
                    <h3>注意</h3>
                    <p>请安装完整的依赖包以获得最佳体验：</p>
                    <pre>pip install -r requirements.txt</pre>
                </div>
                
                <div class="status">
                    <h3>可用功能</h3>
                    <ul>
                        <li>基础Web界面</li>
                        <li>健康检查 API</li>
                        <li>系统状态监控</li>
                    </ul>
                </div>
            </div>
        </body>
        </html>
        """
    
    def run(self, host: str = "0.0.0.0", port: int = 8000, **kwargs):
        """运行Web服务器"""
        if self.dependencies['uvicorn'] and self.app:
            import uvicorn
            logger.info(f"启动FastAPI服务器: http://{host}:{port}")
            uvicorn.run(
                self.app,
                host=host,
                port=port,
                log_level="info",
                **kwargs
            )
        else:
            # 回退到简单HTTP服务器
            self.run_simple_server(host, port)
    
    def run_simple_server(self, host: str = "0.0.0.0", port: int = 8000):
        """运行简单HTTP服务器"""
        import http.server
        import socketserver
        from functools import partial
        
        class SimpleHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, directory=os.path.dirname(__file__), **kwargs)
            
            def do_GET(self):
                if self.path == "/" or self.path == "/dashboard":
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    html = self.server.web_server.get_fallback_html("仪表板")
                    self.wfile.write(html.encode('utf-8'))
                elif self.path == "/health":
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    response = {
                        "status": "healthy",
                        "timestamp": datetime.now().isoformat(),
                        "server": "simple"
                    }
                    self.wfile.write(json.dumps(response).encode('utf-8'))
                else:
                    super().do_GET()
        
        with socketserver.TCPServer((host, port), SimpleHandler) as httpd:
            httpd.web_server = self
            logger.info(f"启动简单HTTP服务器: http://{host}:{port}")
            logger.info("按 Ctrl+C 停止服务器")
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                logger.info("服务器已停止")

def main():
    """主函数"""
    try:
        server = SafeWebServer()
        server.run()
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
