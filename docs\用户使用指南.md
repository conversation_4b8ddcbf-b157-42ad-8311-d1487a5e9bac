# RDS 数据库迁移工具 - 用户使用指南

## 📋 概述

RDS数据库迁移工具是一个功能强大、易于使用的数据库迁移解决方案，支持多种数据库类型之间的数据迁移。本指南将帮助您快速上手并高效使用该工具。

## 🚀 快速开始

### 1. 安装要求

#### 系统要求
- **操作系统**: Windows 10+, Linux, macOS
- **Python版本**: Python 3.8 或更高版本
- **内存**: 建议 4GB 以上
- **磁盘空间**: 至少 1GB 可用空间

#### 依赖安装
```bash
# 克隆项目
git clone <repository-url>
cd rds-migrate

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -m rds_migrate.cli --help
```

### 2. 基本配置

#### 创建配置文件
使用初始化命令创建配置文件模板：

```bash
python -m rds_migrate.cli init --name my_migration
```

这将创建一个名为 `my_migration_config.yaml` 的配置文件。

#### 配置文件示例

```yaml
# 源数据库配置
source:
  host: "source-db.example.com"
  port: 3306
  username: "source_user"
  password: "source_password"
  database: "source_database"
  db_type: "mysql"
  charset: "utf8mb4"

# 目标数据库配置
target:
  host: "target-db.example.com"
  port: 5432
  username: "target_user"
  password: "target_password"
  database: "target_database"
  db_type: "postgresql"

# 迁移选项
batch_size: 1000              # 批次大小
parallel_workers: 4           # 并行工作线程数
create_indexes: true          # 是否创建索引
create_foreign_keys: true     # 是否创建外键
data_only: false             # 仅迁移数据
schema_only: false           # 仅迁移结构

# 表过滤 (可选)
tables:                      # 指定要迁移的表
  - "users"
  - "orders"
  - "products"

exclude_tables:              # 排除的表
  - "temp_table"
  - "log_table"
```

## 🔧 使用方法

### 命令行界面 (CLI)

#### 1. 验证配置
在开始迁移前，验证配置文件的正确性：

```bash
python -m rds_migrate.cli validate --config my_migration_config.yaml
```

#### 2. 测试连接
测试源和目标数据库的连接：

```bash
python -m rds_migrate.cli test --config my_migration_config.yaml
```

#### 3. 分析数据库
分析源数据库，了解数据规模：

```bash
python -m rds_migrate.cli analyze --config my_migration_config.yaml
```

#### 4. 执行迁移
开始数据迁移：

```bash
# 正式迁移
python -m rds_migrate.cli migrate --config my_migration_config.yaml

# 干运行 (仅显示将要执行的操作，不实际迁移)
python -m rds_migrate.cli migrate --config my_migration_config.yaml --dry-run

# 显示详细进度
python -m rds_migrate.cli migrate --config my_migration_config.yaml --progress
```

### Web界面

#### 启动Web界面
```bash
streamlit run web_app.py
```

然后在浏览器中访问 `http://localhost:8501`

#### Web界面功能
- **配置管理**: 可视化配置编辑
- **连接测试**: 一键测试数据库连接
- **迁移监控**: 实时查看迁移进度
- **日志查看**: 查看详细的迁移日志

## 📊 支持的数据库

### 源数据库和目标数据库

| 数据库 | 作为源 | 作为目标 | 驱动程序 | 备注 |
|--------|--------|----------|----------|------|
| MySQL | ✅ | ✅ | PyMySQL | 支持 5.7+ |
| PostgreSQL | ✅ | ✅ | psycopg2 | 支持 10+ |
| SQL Server | ✅ | ✅ | pyodbc | 支持 2016+ |
| Oracle | ✅ | ✅ | cx_Oracle | 支持 12c+ |
| SQLite | ✅ | ✅ | 内置 | 主要用于测试 |

### 数据库特定配置

#### MySQL
```yaml
source:
  host: "mysql-server"
  port: 3306
  db_type: "mysql"
  charset: "utf8mb4"
  ssl_mode: "REQUIRED"  # 可选: DISABLED, PREFERRED, REQUIRED
```

#### PostgreSQL
```yaml
target:
  host: "postgres-server"
  port: 5432
  db_type: "postgresql"
  ssl_mode: "require"   # 可选: disable, allow, prefer, require
```

#### SQL Server
```yaml
source:
  host: "sqlserver-host"
  port: 1433
  db_type: "sqlserver"
  # 注意: 需要安装 ODBC Driver 17 for SQL Server
```

## ⚙️ 高级配置

### 性能调优

#### 批次大小优化
```yaml
batch_size: 5000  # 大表使用更大的批次
```

**建议值**:
- 小表 (< 10万行): 1000-2000
- 中表 (10万-100万行): 2000-5000
- 大表 (> 100万行): 5000-10000

#### 并行度设置
```yaml
parallel_workers: 8  # 根据CPU核心数调整
```

**建议值**:
- CPU核心数的 1-2 倍
- 考虑数据库连接限制
- 网络带宽限制

### 迁移模式

#### 仅结构迁移
```yaml
schema_only: true
data_only: false
```

#### 仅数据迁移
```yaml
schema_only: false
data_only: true
```

#### 增量迁移
```yaml
# 使用表过滤实现增量迁移
tables:
  - "new_table_1"
  - "new_table_2"
```

### 表过滤策略

#### 包含特定表
```yaml
tables:
  - "users"
  - "orders"
  - "products"
```

#### 排除特定表
```yaml
exclude_tables:
  - "temp_*"      # 支持通配符
  - "log_table"
  - "cache_*"
```

#### 复杂过滤
```yaml
# 迁移所有表，但排除临时表和日志表
exclude_tables:
  - "temp_*"
  - "*_log"
  - "*_cache"
  - "session_*"
```

## 🔍 监控和日志

### 日志配置
日志文件位置: `logs/migration.log`

#### 日志级别
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息 (默认)
- **WARNING**: 警告信息
- **ERROR**: 错误信息

#### 自定义日志级别
```bash
export LOG_LEVEL=DEBUG
python -m rds_migrate.cli migrate --config config.yaml
```

### 进度监控

#### 命令行进度
```bash
python -m rds_migrate.cli migrate --config config.yaml --progress
```

#### Web界面监控
访问 `http://localhost:8501` 查看实时进度。

### 性能指标
迁移完成后，工具会显示：
- 迁移耗时
- 数据传输速度
- 每个表的迁移统计
- 错误和警告汇总

## 🚨 故障排除

### 常见问题

#### 1. 连接失败
**症状**: `DatabaseConnectionError: Failed to connect`

**解决方案**:
- 检查网络连接
- 验证数据库服务是否运行
- 确认用户名和密码正确
- 检查防火墙设置

#### 2. 权限不足
**症状**: `Access denied` 或 `Permission denied`

**解决方案**:
- 确保用户有读取源数据库的权限
- 确保用户有写入目标数据库的权限
- 检查表级别的权限设置

#### 3. 内存不足
**症状**: `MemoryError` 或系统变慢

**解决方案**:
- 减小 `batch_size` 值
- 减少 `parallel_workers` 数量
- 增加系统内存

#### 4. 数据类型不兼容
**症状**: 特定表迁移失败

**解决方案**:
- 检查源和目标数据库的数据类型映射
- 使用 `schema_only: true` 先迁移结构
- 手动调整目标表结构

### 调试技巧

#### 启用详细日志
```bash
export LOG_LEVEL=DEBUG
python -m rds_migrate.cli migrate --config config.yaml
```

#### 干运行测试
```bash
python -m rds_migrate.cli migrate --config config.yaml --dry-run
```

#### 单表测试
```yaml
# 在配置文件中只指定一个表进行测试
tables:
  - "test_table"
```

## 📈 最佳实践

### 迁移前准备
1. **备份数据**: 始终备份源数据库
2. **测试环境**: 先在测试环境验证迁移
3. **网络稳定**: 确保网络连接稳定
4. **资源规划**: 评估所需时间和资源

### 迁移过程
1. **分批迁移**: 大型数据库分批次迁移
2. **监控进度**: 实时监控迁移状态
3. **错误处理**: 及时处理迁移错误
4. **性能调优**: 根据实际情况调整参数

### 迁移后验证
1. **数据一致性**: 验证数据完整性
2. **性能测试**: 测试目标数据库性能
3. **应用测试**: 测试应用程序兼容性
4. **索引优化**: 根据需要优化索引

## 🔗 相关资源

- **技术架构文档**: `docs/技术架构文档.md`
- **API文档**: `docs/API文档.md`
- **测试报告**: `docs/测试修复成果报告.md`
- **示例配置**: `examples/` 目录

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 参考本文档的故障排除部分
3. 检查GitHub Issues中的已知问题
4. 提交新的Issue并提供详细信息

---

*本文档最后更新: 2025-01-26*
