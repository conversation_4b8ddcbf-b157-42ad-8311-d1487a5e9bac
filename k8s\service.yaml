# Kubernetes Service配置
apiVersion: v1
kind: Service
metadata:
  name: rds-migrate-service
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: app
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  - name: api
    port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: rds-migration-tool
    component: app

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: rds-migrate
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP
  selector:
    app: redis

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: rds-migrate
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: postgres

---
# LoadBalancer Service for external access
apiVersion: v1
kind: Service
metadata:
  name: rds-migrate-lb
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: loadbalancer
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: https
    port: 443
    targetPort: 8000
    protocol: TCP
  selector:
    app: rds-migration-tool
    component: app
  loadBalancerSourceRanges:
  - 10.0.0.0/8
  - **********/12
  - ***********/16
