# RDS Migration Tool - Usage Guide

This guide provides step-by-step instructions for using the RDS Migration Tool in various scenarios.

## Table of Contents

1. [Installation and Setup](#installation-and-setup)
2. [Basic Migration Workflow](#basic-migration-workflow)
3. [Common Migration Scenarios](#common-migration-scenarios)
4. [Advanced Configuration](#advanced-configuration)
5. [Troubleshooting](#troubleshooting)
6. [Performance Optimization](#performance-optimization)

## Installation and Setup

### 1. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install database-specific drivers
pip install pymysql psycopg2-binary pyodbc
```

### 2. Verify Installation

```bash
python main.py --help
```

## Basic Migration Workflow

### Step 1: Create Configuration

Use the interactive wizard for first-time setup:

```bash
python main.py interactive
```

Or create a sample configuration:

```bash
python main.py init --output my_config.yaml
```

### Step 2: Edit Configuration

Edit the generated YAML file with your database details:

```yaml
source:
  host: "source-database.com"
  port: 3306
  database: "source_db"
  username: "source_user"
  password: "source_password"
  db_type: "mysql"

target:
  host: "target-rds.amazonaws.com"
  port: 3306
  database: "target_db"
  username: "target_user"
  password: "target_password"
  db_type: "mysql"
```

### Step 3: Validate Configuration

```bash
python main.py validate -c my_config.yaml
```

### Step 4: Test Connections

```bash
python main.py test -c my_config.yaml
```

### Step 5: Analyze Source Database

```bash
python main.py analyze -c my_config.yaml
```

### Step 6: Run Migration

Start with a dry run:

```bash
python main.py migrate -c my_config.yaml --dry-run
```

Then run the actual migration:

```bash
python main.py migrate -c my_config.yaml --log-level INFO --report migration_report.txt
```

## Common Migration Scenarios

### Scenario 1: Traditional MySQL to AWS RDS

**Use Case**: Moving from on-premise MySQL to AWS RDS MySQL

**Configuration**: See `examples/mysql_to_aws_rds.yaml`

**Steps**:
1. Ensure AWS RDS instance is created and accessible
2. Configure security groups to allow connections
3. Use the provided example configuration
4. Test with a small subset of tables first

```bash
# Test connection
python main.py test -c examples/mysql_to_aws_rds.yaml

# Analyze source
python main.py analyze -c examples/mysql_to_aws_rds.yaml

# Migrate
python main.py migrate -c examples/mysql_to_aws_rds.yaml
```

### Scenario 2: PostgreSQL to PostgreSQL (Different Providers)

**Use Case**: Moving from Heroku PostgreSQL to Azure PostgreSQL

**Configuration**: See `examples/postgres_to_postgres.yaml`

**Special Considerations**:
- SSL configuration may differ between providers
- Check for provider-specific extensions
- Verify user permissions and roles

### Scenario 3: Cross-Database Migration (MySQL to PostgreSQL)

**Use Case**: Modernizing from MySQL to PostgreSQL

**Configuration**: See `examples/mysql_to_postgresql.yaml`

**Important Notes**:
- Data type mappings may require manual adjustment
- Foreign key constraints might need review
- Consider running schema-only first, then data-only

```bash
# First migrate schema only
python main.py migrate -c examples/mysql_to_postgresql.yaml --schema-only

# Review and adjust schema manually if needed

# Then migrate data only
python main.py migrate -c examples/mysql_to_postgresql.yaml --data-only
```

### Scenario 4: Schema-Only Migration

**Use Case**: Setting up development/staging environments

**Configuration**: See `examples/schema_only_migration.yaml`

```bash
python main.py migrate -c examples/schema_only_migration.yaml
```

### Scenario 5: Data-Only Migration

**Use Case**: Refreshing data in existing schema

**Configuration**: See `examples/data_only_migration.yaml`

```bash
python main.py migrate -c examples/data_only_migration.yaml
```

## Advanced Configuration

### Environment Variables

Store sensitive information in environment variables:

```bash
export SOURCE_DB_PASSWORD="your_source_password"
export TARGET_DB_PASSWORD="your_target_password"
```

Reference in configuration:

```yaml
source:
  password: "${SOURCE_DB_PASSWORD}"
target:
  password: "${TARGET_DB_PASSWORD}"
```

### Custom Table Selection

```yaml
migration:
  # Include only specific tables
  tables:
    - "users"
    - "orders"
    - "products"
  
  # Exclude specific tables
  exclude_tables:
    - "temp_table"
    - "log_table"
    - "cache_table"
```

### Performance Tuning

```yaml
migration:
  batch_size: 2000        # Increase for better performance
  parallel_workers: 6     # Adjust based on server capacity
```

### SSL Configuration

For PostgreSQL with SSL:

```yaml
source:
  ssl_mode: "require"     # require, prefer, allow, disable
```

## Troubleshooting

### Connection Issues

**Problem**: Cannot connect to database

**Solutions**:
1. Check network connectivity: `telnet hostname port`
2. Verify credentials
3. Check firewall rules
4. Ensure database server is running

### Permission Issues

**Problem**: Access denied errors

**Solutions**:
1. Verify user has SELECT permissions on source
2. Verify user has CREATE, INSERT permissions on target
3. Check for database-specific permission requirements

### Memory Issues

**Problem**: Out of memory errors

**Solutions**:
1. Reduce batch size: `batch_size: 500`
2. Reduce parallel workers: `parallel_workers: 2`
3. Migrate tables individually

### Data Type Issues

**Problem**: Data type conversion errors

**Solutions**:
1. Review source and target schema compatibility
2. Consider custom data transformation
3. Migrate schema first, then adjust manually

### Performance Issues

**Problem**: Migration is too slow

**Solutions**:
1. Increase batch size (if memory allows)
2. Increase parallel workers
3. Ensure indexes are created after data migration
4. Use faster network connection

## Performance Optimization

### Network Optimization

- Use dedicated network connections when possible
- Consider running migration from a server close to both databases
- Monitor network bandwidth usage

### Database Optimization

**Source Database**:
- Ensure adequate read capacity
- Consider read replicas for large migrations
- Monitor CPU and I/O usage

**Target Database**:
- Ensure adequate write capacity
- Consider temporarily disabling foreign key checks
- Monitor disk space and I/O

### Migration Settings

```yaml
migration:
  batch_size: 5000        # Larger batches for better throughput
  parallel_workers: 8     # More workers for parallel processing
  create_indexes: false   # Create indexes after data migration
  create_foreign_keys: false  # Create constraints after migration
```

### Monitoring Progress

Use detailed logging and reports:

```bash
python main.py migrate -c config.yaml \
  --log-level DEBUG \
  --log-file migration.log \
  --report detailed_report.txt
```

### Best Practices

1. **Test First**: Always run dry-run and test with subset of data
2. **Backup**: Create backups before migration
3. **Monitor**: Watch system resources during migration
4. **Validate**: Verify data integrity after migration
5. **Document**: Keep detailed records of migration process

### Post-Migration Validation

```bash
# Compare row counts
python main.py analyze -c config.yaml --table users

# Run data integrity checks
# (implement custom validation scripts as needed)
```

## Getting Help

- Check the main README.md for detailed documentation
- Review example configurations in the `examples/` directory
- Enable DEBUG logging for detailed troubleshooting information
- Monitor system resources during migration
