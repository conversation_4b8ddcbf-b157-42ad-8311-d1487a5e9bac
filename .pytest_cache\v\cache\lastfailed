{"tests/test_cli.py::TestCLI::test_init_command_default_name": true, "tests/test_cli.py::TestCLI::test_validate_command_failure": true, "tests/test_cli.py::TestCLI::test_analyze_command": true, "tests/test_cli.py::TestCLI::test_migrate_with_progress": true, "tests/test_cli.py::TestCLI::test_migrate_dry_run": true, "tests/test_cli.py::TestCLI::test_file_not_found_error": true, "tests/test_cli.py::TestCLI::test_invalid_config_format": true, "tests/test_config.py::TestDatabaseConfig::test_database_config_validation": true, "tests/test_config.py::TestDatabaseConfig::test_connection_string_generation": true, "tests/test_config.py::TestDatabaseConfig::test_postgresql_connection_string": true, "tests/test_config.py::TestConfigManager::test_validate_config": true, "tests/test_config.py::TestConfigManager::test_load_invalid_json": true, "tests/test_config.py::TestMigrationConfig::test_migration_config_validation": true, "tests/test_database.py::TestDatabaseManager::test_disconnect_with_exception": true, "tests/test_database.py::TestDatabaseManager::test_execute_query_no_connection": true, "tests/test_database.py::TestDatabaseManager::test_get_table_list": true, "tests/test_database.py::TestDatabaseManager::test_get_table_schema": true, "tests/test_database.py::TestDatabaseManager::test_get_table_count": true, "tests/test_database.py::TestDatabaseManager::test_table_exists_true": true, "tests/test_database.py::TestDatabaseManager::test_table_exists_false": true, "tests/test_database.py::TestDatabaseManager::test_create_table": true, "tests/test_database.py::TestDatabaseManager::test_insert_data": true, "tests/test_database.py::TestDatabaseManager::test_context_manager": true, "tests/test_database.py::TestDatabaseManager::test_get_connection_string": true, "tests/test_integration.py::TestIntegration::test_cli_integration": true, "tests/test_integration.py::TestIntegration::test_error_handling_integration": true, "tests/test_integration.py::TestIntegration::test_progress_callback_integration": true, "tests/test_integration.py::TestIntegration::test_batch_processing_integration": true, "tests/test_integration.py::TestIntegration::test_schema_only_migration_integration": true, "tests/test_integration.py::TestIntegration::test_data_only_migration_integration": true, "tests/test_migrator.py::TestMigrationEngine::test_initialize_success": true, "tests/test_migrator.py::TestMigrationEngine::test_initialize_source_failure": true, "tests/test_migrator.py::TestMigrationEngine::test_initialize_target_failure": true, "tests/test_migrator.py::TestMigrationEngine::test_cleanup": true, "tests/test_migrator.py::TestMigrationEngine::test_get_tables_to_migrate_specific": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_schema_exists": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_data": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_data_with_progress": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_full_success": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_initialization_failure": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_schema_only": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_data_only": true, "tests/test_migrator.py::TestMigrationEngine::test_validate_migration_success": true, "tests/test_migrator.py::TestMigrationEngine::test_validate_migration_failure": true, "tests/test_performance.py::TestPerformance::test_large_data_migration_performance": true, "tests/test_performance.py::TestPerformance::test_concurrent_table_migration": true, "tests/test_performance.py::TestPerformance::test_memory_usage_during_migration": true, "tests/test_performance.py::TestPerformance::test_connection_pool_performance": true, "tests/test_performance.py::TestPerformance::test_batch_size_optimization": true, "tests/test_database.py::TestDatabaseManager::test_get_row_count": true, "tests/test_database.py::TestDatabaseManager::test_get_database_info": true, "tests/test_database.py::TestDatabaseManager::test_get_row_count_mock": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_schema_failure": true, "tests/test_migrator.py::TestMigrationEngine::test_migrate_success": true}