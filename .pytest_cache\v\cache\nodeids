["simple_test.py::test_documentation", "simple_test.py::test_example_files", "simple_test.py::test_file_structure", "simple_test.py::test_main_entry_point", "simple_test.py::test_python_imports", "test_tool.py::test_cli_help", "test_tool.py::test_config_management", "test_tool.py::test_database_config_creation", "test_tool.py::test_example_configs", "test_tool.py::test_file_structure", "test_tool.py::test_logging_system", "tests/test_cli.py::TestCLI::test_analyze_command", "tests/test_cli.py::TestCLI::test_file_not_found_error", "tests/test_cli.py::TestCLI::test_init_command", "tests/test_cli.py::TestCLI::test_init_command_default_name", "tests/test_cli.py::TestCLI::test_invalid_config_format", "tests/test_cli.py::TestCLI::test_main_help", "tests/test_cli.py::TestCLI::test_migrate_command_failure", "tests/test_cli.py::TestCLI::test_migrate_command_success", "tests/test_cli.py::TestCLI::test_migrate_dry_run", "tests/test_cli.py::TestCLI::test_migrate_with_progress", "tests/test_cli.py::TestCLI::test_test_command_source_failure", "tests/test_cli.py::TestCLI::test_test_command_success", "tests/test_cli.py::TestCLI::test_validate_command_failure", "tests/test_cli.py::TestCLI::test_validate_command_success", "tests/test_cli.py::TestCLI::test_version_option", "tests/test_config.py::TestConfigManager::test_create_sample_config", "tests/test_config.py::TestConfigManager::test_load_invalid_json", "tests/test_config.py::TestConfigManager::test_load_invalid_yaml", "tests/test_config.py::TestConfigManager::test_load_json_config", "tests/test_config.py::TestConfigManager::test_load_nonexistent_config", "tests/test_config.py::TestConfigManager::test_load_yaml_config", "tests/test_config.py::TestConfigManager::test_save_config", "tests/test_config.py::TestConfigManager::test_validate_config", "tests/test_config.py::TestDatabaseConfig::test_connection_string_generation", "tests/test_config.py::TestDatabaseConfig::test_database_config_charset", "tests/test_config.py::TestDatabaseConfig::test_database_config_creation", "tests/test_config.py::TestDatabaseConfig::test_database_config_to_dict", "tests/test_config.py::TestDatabaseConfig::test_database_config_validation", "tests/test_config.py::TestDatabaseConfig::test_database_config_with_ssl", "tests/test_config.py::TestDatabaseConfig::test_postgresql_connection_string", "tests/test_config.py::TestMigrationConfig::test_migration_config_creation", "tests/test_config.py::TestMigrationConfig::test_migration_config_defaults", "tests/test_config.py::TestMigrationConfig::test_migration_config_to_dict", "tests/test_config.py::TestMigrationConfig::test_migration_config_validation", "tests/test_database.py::TestDatabaseManager::test_build_connection_string_mysql", "tests/test_database.py::TestDatabaseManager::test_build_connection_string_postgresql", "tests/test_database.py::TestDatabaseManager::test_connect_failure", "tests/test_database.py::TestDatabaseManager::test_connect_success", "tests/test_database.py::TestDatabaseManager::test_connection_string_property", "tests/test_database.py::TestDatabaseManager::test_context_manager", "tests/test_database.py::TestDatabaseManager::test_create_table", "tests/test_database.py::TestDatabaseManager::test_database_manager_creation", "tests/test_database.py::TestDatabaseManager::test_disconnect", "tests/test_database.py::TestDatabaseManager::test_disconnect_no_engine", "tests/test_database.py::TestDatabaseManager::test_disconnect_with_exception", "tests/test_database.py::TestDatabaseManager::test_execute_query_failure", "tests/test_database.py::TestDatabaseManager::test_execute_query_no_connection", "tests/test_database.py::TestDatabaseManager::test_execute_query_success", "tests/test_database.py::TestDatabaseManager::test_get_connection_context_manager", "tests/test_database.py::TestDatabaseManager::test_get_connection_string", "tests/test_database.py::TestDatabaseManager::test_get_database_info", "tests/test_database.py::TestDatabaseManager::test_get_row_count", "tests/test_database.py::TestDatabaseManager::test_get_row_count_mock", "tests/test_database.py::TestDatabaseManager::test_get_table_count", "tests/test_database.py::TestDatabaseManager::test_get_table_list", "tests/test_database.py::TestDatabaseManager::test_get_table_list_mock", "tests/test_database.py::TestDatabaseManager::test_get_table_schema", "tests/test_database.py::TestDatabaseManager::test_insert_data", "tests/test_database.py::TestDatabaseManager::test_table_exists_false", "tests/test_database.py::TestDatabaseManager::test_table_exists_true", "tests/test_database.py::TestDatabaseManager::test_test_connection_failure", "tests/test_database.py::TestDatabaseManager::test_test_connection_success", "tests/test_integration.py::TestIntegration::test_batch_processing_integration", "tests/test_integration.py::TestIntegration::test_cli_integration", "tests/test_integration.py::TestIntegration::test_config_file_integration", "tests/test_integration.py::TestIntegration::test_data_only_migration_integration", "tests/test_integration.py::TestIntegration::test_error_handling_integration", "tests/test_integration.py::TestIntegration::test_full_migration_workflow", "tests/test_integration.py::TestIntegration::test_progress_callback_integration", "tests/test_integration.py::TestIntegration::test_schema_only_migration_integration", "tests/test_integration.py::TestIntegration::test_table_filtering_integration", "tests/test_migrator.py::TestMigrationEngine::test_cleanup", "tests/test_migrator.py::TestMigrationEngine::test_connect_databases_failure", "tests/test_migrator.py::TestMigrationEngine::test_connect_databases_success", "tests/test_migrator.py::TestMigrationEngine::test_disconnect_databases", "tests/test_migrator.py::TestMigrationEngine::test_get_tables_to_migrate_all", "tests/test_migrator.py::TestMigrationEngine::test_get_tables_to_migrate_specific", "tests/test_migrator.py::TestMigrationEngine::test_initialize_source_failure", "tests/test_migrator.py::TestMigrationEngine::test_initialize_success", "tests/test_migrator.py::TestMigrationEngine::test_initialize_target_failure", "tests/test_migrator.py::TestMigrationEngine::test_migrate_data_only", "tests/test_migrator.py::TestMigrationEngine::test_migrate_full_success", "tests/test_migrator.py::TestMigrationEngine::test_migrate_initialization_failure", "tests/test_migrator.py::TestMigrationEngine::test_migrate_no_tables", "tests/test_migrator.py::TestMigrationEngine::test_migrate_schema_only", "tests/test_migrator.py::TestMigrationEngine::test_migrate_success", "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_data", "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_data_with_progress", "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_schema", "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_schema_exists", "tests/test_migrator.py::TestMigrationEngine::test_migrate_table_schema_failure", "tests/test_migrator.py::TestMigrationEngine::test_migration_engine_creation", "tests/test_migrator.py::TestMigrationEngine::test_set_progress_callback", "tests/test_migrator.py::TestMigrationEngine::test_validate_migration_failure", "tests/test_migrator.py::TestMigrationEngine::test_validate_migration_success", "tests/test_performance.py::TestPerformance::test_batch_size_optimization", "tests/test_performance.py::TestPerformance::test_concurrent_table_migration", "tests/test_performance.py::TestPerformance::test_connection_pool_performance", "tests/test_performance.py::TestPerformance::test_large_data_migration_performance", "tests/test_performance.py::TestPerformance::test_memory_usage_during_migration", "tests/test_performance.py::TestPerformance::test_stress_test"]