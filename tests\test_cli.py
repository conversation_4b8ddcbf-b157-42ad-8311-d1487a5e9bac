"""
CLI模块测试
"""

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, Mock
import tempfile
import os

from rds_migrate.cli import cli


@pytest.mark.unit
@pytest.mark.cli
class TestCLI:
    """CLI测试"""

    def setup_method(self):
        """设置测试方法"""
        self.runner = CliRunner()

    def test_main_help(self):
        """测试主命令帮助"""
        result = self.runner.invoke(cli, ['--help'])

        assert result.exit_code == 0
        assert 'RDS Migration Tool' in result.output
        assert 'init' in result.output
        assert 'validate' in result.output
        assert 'migrate' in result.output
    
    def test_init_command(self):
        """测试init命令"""
        with tempfile.TemporaryDirectory() as tmpdir:
            config_file = os.path.join(tmpdir, 'test_config.yaml')

            result = self.runner.invoke(cli, ['init', '--output', config_file])

            assert result.exit_code == 0
            assert os.path.exists(config_file)
            assert 'Sample configuration created' in result.output

    def test_init_command_default_name(self):
        """测试init命令默认文件名"""
        with tempfile.TemporaryDirectory() as tmpdir:
            os.chdir(tmpdir)

            result = self.runner.invoke(cli, ['init'])

            assert result.exit_code == 0
            assert os.path.exists('sample_config.yaml')
    
    @patch('rds_migrate.cli.ConfigManager')
    def test_validate_command_success(self, mock_config_manager):
        """测试validate命令成功"""
        mock_manager = Mock()
        mock_config = Mock()
        mock_config.validate.return_value = True
        mock_manager.load_config.return_value = mock_config
        mock_config_manager.return_value = mock_manager
        
        result = self.runner.invoke(cli, ['validate', '--config', 'test_config.yaml'])
        
        assert result.exit_code == 0
        assert 'Configuration is valid' in result.output
    
    @patch('rds_migrate.cli.ConfigManager')
    def test_validate_command_failure(self, mock_config_manager):
        """测试validate命令失败"""
        mock_manager = Mock()
        mock_config = Mock()
        mock_config.validate.return_value = False
        mock_manager.load_config.return_value = mock_config
        mock_config_manager.return_value = mock_manager
        
        result = self.runner.invoke(cli, ['validate', '--config', 'test_config.yaml'])
        
        assert result.exit_code == 1
        assert 'Configuration is invalid' in result.output
    
    @patch('rds_migrate.cli.DatabaseManager')
    def test_test_command_success(self, mock_db_manager):
        """测试test命令成功"""
        mock_source_db = Mock()
        mock_target_db = Mock()
        mock_source_db.test_connection.return_value = True
        mock_target_db.test_connection.return_value = True
        mock_source_db.get_database_info.return_value = {'db_type': 'mysql', 'version': '8.0'}
        mock_target_db.get_database_info.return_value = {'db_type': 'mysql', 'version': '8.0'}
        mock_db_manager.side_effect = [mock_source_db, mock_target_db]

        with patch('rds_migrate.cli.ConfigManager') as mock_config_manager:
            mock_manager = Mock()
            mock_config = Mock()
            mock_config.source = Mock()  # 添加source属性
            mock_config.target = Mock()  # 添加target属性
            mock_manager.load_config.return_value = mock_config
            mock_config_manager.return_value = mock_manager

            result = self.runner.invoke(cli, ['test', '--config', 'test_config.yaml'])

            assert result.exit_code == 0
            assert 'Source database connection successful' in result.output
            assert 'Target database connection successful' in result.output
    
    @patch('rds_migrate.cli.DatabaseManager')
    def test_test_command_source_failure(self, mock_db_manager):
        """测试test命令源数据库连接失败"""
        mock_source_db = Mock()
        mock_source_db.test_connection.return_value = False
        mock_db_manager.return_value = mock_source_db

        with patch('rds_migrate.cli.ConfigManager') as mock_config_manager:
            mock_manager = Mock()
            mock_config = Mock()
            mock_config.source = Mock()  # 添加source属性
            mock_manager.load_config.return_value = mock_config
            mock_config_manager.return_value = mock_manager

            result = self.runner.invoke(cli, ['test', '--config', 'test_config.yaml'])

            assert result.exit_code == 1
            assert 'Source database connection failed' in result.output

    @patch('rds_migrate.cli.MigrationEngine')
    @patch('rds_migrate.cli.MigrationLogger')
    @patch('rds_migrate.cli.ProgressTracker')
    @patch('rds_migrate.cli.MigrationReporter')
    def test_migrate_command_success(self, mock_reporter, mock_progress, mock_logger, mock_migration_engine):
        """测试migrate命令成功"""
        # Mock migration engine
        mock_engine = Mock()
        mock_engine.source_db = Mock()
        mock_engine.target_db = Mock()
        mock_engine.source_db.get_database_info.return_value = {'db_type': 'mysql', 'version': '8.0'}
        mock_engine.target_db.get_database_info.return_value = {'db_type': 'mysql', 'version': '8.0'}
        mock_engine.get_tables_to_migrate.return_value = ['users', 'orders']
        mock_engine.migrate.return_value = Mock(tables_migrated=2, rows_migrated=1000)
        mock_migration_engine.return_value = mock_engine

        # Mock logger
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_logger_instance.get_logger.return_value = Mock()

        with patch('rds_migrate.cli.ConfigManager') as mock_config_manager:
            mock_manager = Mock()
            mock_config = Mock()
            mock_manager.load_config.return_value = mock_config
            mock_manager.validate_config.return_value = None
            mock_config_manager.return_value = mock_manager

            result = self.runner.invoke(cli, ['migrate', '--config', 'test_config.yaml'])

            assert result.exit_code == 0
    
    @patch('rds_migrate.cli.MigrationEngine')
    def test_migrate_command_failure(self, mock_migration_engine):
        """测试migrate命令失败"""
        mock_engine = Mock()
        mock_engine.migrate.return_value = False
        mock_migration_engine.return_value = mock_engine
        
        with patch('rds_migrate.cli.ConfigManager') as mock_config_manager:
            mock_manager = Mock()
            mock_config = Mock()
            mock_manager.load_config.return_value = mock_config
            mock_config_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['migrate', '--config', 'test_config.yaml'])

            assert result.exit_code == 1
            assert 'Migration failed' in result.output

    @patch('rds_migrate.cli.DatabaseManager')
    def test_analyze_command(self, mock_db_manager):
        """测试analyze命令"""
        mock_db = Mock()
        mock_db.connect.return_value = True
        mock_db.get_table_list.return_value = ['users', 'orders', 'products']
        mock_db.get_table_count.side_effect = [1000, 500, 2000]
        mock_db_manager.return_value = mock_db

        with patch('rds_migrate.cli.ConfigManager') as mock_config_manager:
            mock_manager = Mock()
            mock_config = Mock()
            mock_manager.load_config.return_value = mock_config
            mock_config_manager.return_value = mock_manager

            result = self.runner.invoke(cli, ['analyze', '--config', 'test_config.yaml'])
            
            assert result.exit_code == 0
            assert 'Database Analysis Report' in result.output
            assert 'users' in result.output
            assert 'orders' in result.output
            assert 'products' in result.output
    
    def test_migrate_with_progress(self):
        """测试带进度显示的迁移"""
        with patch('rds_migrate.cli.MigrationEngine') as mock_migration_engine, \
             patch('rds_migrate.cli.ConfigManager') as mock_config_manager:
            
            mock_engine = Mock()
            mock_engine.migrate.return_value = True
            mock_migration_engine.return_value = mock_engine
            
            mock_manager = Mock()
            mock_config = Mock()
            mock_manager.load_config.return_value = mock_config
            mock_config_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['migrate', '--config', 'test_config.yaml', '--progress'])

            assert result.exit_code == 0
            # 验证进度回调被设置
            assert mock_engine.progress_callback is not None

    def test_migrate_dry_run(self):
        """测试干运行模式"""
        with patch('rds_migrate.cli.MigrationEngine') as mock_migration_engine, \
             patch('rds_migrate.cli.ConfigManager') as mock_config_manager:

            mock_engine = Mock()
            mock_engine.get_tables_to_migrate.return_value = ['users', 'orders']
            mock_migration_engine.return_value = mock_engine

            mock_manager = Mock()
            mock_config = Mock()
            mock_manager.load_config.return_value = mock_config
            mock_config_manager.return_value = mock_manager

            result = self.runner.invoke(cli, ['migrate', '--config', 'test_config.yaml', '--dry-run'])
            
            assert result.exit_code == 0
            assert 'Dry run mode' in result.output
            assert 'users' in result.output
            assert 'orders' in result.output
            # 验证实际迁移没有执行
            mock_engine.migrate.assert_not_called()
    
    def test_file_not_found_error(self):
        """测试配置文件不存在错误"""
        result = self.runner.invoke(cli, ['validate', '--config', 'nonexistent_config.yaml'])

        assert result.exit_code == 1
        assert 'Error' in result.output

    def test_invalid_config_format(self):
        """测试无效配置格式"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write('invalid: yaml: content:')
            f.flush()

            try:
                result = self.runner.invoke(cli, ['validate', '--config', f.name])
                
                assert result.exit_code == 1
                assert 'Error' in result.output
            finally:
                os.unlink(f.name)
    
    def test_version_option(self):
        """测试版本选项"""
        result = self.runner.invoke(cli, ['--version'])

        assert result.exit_code == 0
        # 应该显示版本信息
