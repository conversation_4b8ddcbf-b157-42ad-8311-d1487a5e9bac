[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 最小版本要求
minversion = 6.0

# 默认选项
addopts = 
    -ra
    --strict-markers
    --strict-config
    --disable-warnings
    --tb=short

# 测试标记定义
markers =
    unit: 单元测试标记
    integration: 集成测试标记
    performance: 性能测试标记
    slow: 慢速测试标记（运行时间较长）
    database: 需要数据库连接的测试
    cli: CLI相关测试
    config: 配置相关测试
    migration: 迁移功能测试

# Python文件模式
python_files = test_*.py *_test.py

# Python类模式
python_classes = Test*

# Python函数模式
python_functions = test_*

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 覆盖率配置
[coverage:run]
source = rds_migrate
omit = 
    */tests/*
    */venv/*
    */env/*
    setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
