# 🗄️ RDS数据库迁移工具 - 快速开始指南

## 📋 概述

RDS迁移工具是一个功能强大的数据库迁移解决方案，支持多种数据库类型之间的数据迁移，包括传统数据库、云RDS和不同RDS提供商之间的迁移。

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
python start_rds_migrate.py
```

这个脚本会：
- 自动检测依赖包
- 提供安装选项
- 选择最佳的启动方式（GUI/CLI/简化版）

### 方法二：安装依赖后启动

1. **安装依赖包**：
```bash
# 使用安装脚本（推荐）
python install_dependencies.py

# 或手动安装
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
```

2. **启动程序**：
```bash
# GUI界面（推荐）
python -m rds_migrate.gui.main_window

# CLI命令行
python -m rds_migrate.cli --help

# 简化版本（无外部依赖）
python simple_migrator.py help
```

## 🛠️ 使用方法

### 1. 创建配置文件

```bash
# CLI方式
python -m rds_migrate.cli init

# 简化版方式
python simple_migrator.py init my_config.json
```

### 2. 编辑配置文件

编辑生成的配置文件，填入您的数据库连接信息：

```yaml
source:
  db_type: mysql
  host: source-db.example.com
  port: 3306
  database: source_db
  username: source_user
  password: source_password

target:
  db_type: mysql
  host: target-rds.amazonaws.com
  port: 3306
  database: target_db
  username: target_user
  password: target_password

# 迁移设置
batch_size: 1000
parallel_workers: 4
tables:
  - users
  - orders
  - products
```

### 3. 测试连接

```bash
# CLI方式
python -m rds_migrate.cli test sample_config.yaml

# 简化版方式
python simple_migrator.py test my_config.json
```

### 4. 执行迁移

```bash
# CLI方式
python -m rds_migrate.cli migrate sample_config.yaml

# 简化版方式
python simple_migrator.py migrate my_config.json
```

## 🎯 支持的数据库

- **MySQL** (包括云RDS)
- **PostgreSQL** (包括云RDS)
- **SQL Server** (包括云RDS)
- **Oracle** (部分支持)

## 📁 项目结构

```
RDS Migrate/
├── rds_migrate/           # 主程序包
│   ├── cli.py            # 命令行界面
│   ├── gui/              # 图形界面
│   ├── config.py         # 配置管理
│   ├── database.py       # 数据库操作
│   ├── migrator.py       # 迁移引擎
│   └── ...
├── simple_migrator.py    # 简化版本（无外部依赖）
├── start_rds_migrate.py  # 一键启动脚本
├── install_dependencies.py # 依赖安装脚本
├── requirements.txt      # 依赖列表
└── 快速开始.md          # 本文件
```

## 🔧 故障排除

### 依赖安装问题

如果遇到依赖安装失败：

1. **使用安装脚本**：
```bash
python install_dependencies.py
```

2. **手动使用国内镜像**：
```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ click pyyaml tabulate sqlalchemy pymysql psycopg2-binary
```

3. **使用简化版本**：
```bash
python simple_migrator.py help
```

### 程序启动问题

如果程序无法启动：

1. **检查Python版本**（需要Python 3.7+）：
```bash
python --version
```

2. **使用测试脚本**：
```bash
python test_imports.py
```

3. **使用简化版本**：
```bash
python simple_migrator.py help
```

## 📞 获取帮助

- 运行 `python start_rds_migrate.py` 获取交互式帮助
- 运行 `python -m rds_migrate.cli --help` 查看CLI帮助
- 运行 `python simple_migrator.py help` 查看简化版帮助

## ✨ 特性

- 🎨 **多种界面**：GUI图形界面、CLI命令行、简化版本
- 🔄 **自动依赖管理**：智能检测和安装依赖包
- 🌐 **国内镜像支持**：使用清华、阿里云等镜像加速下载
- 📊 **进度监控**：实时显示迁移进度
- 🛡️ **错误处理**：完善的错误处理和恢复机制
- 🔧 **灵活配置**：支持YAML和JSON配置格式

开始您的数据库迁移之旅吧！🚀
