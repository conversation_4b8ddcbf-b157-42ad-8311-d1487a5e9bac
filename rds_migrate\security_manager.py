"""
安全管理器 - 配置加密、敏感信息保护和访问控制
"""

import os
import base64
import hashlib
import secrets
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path
import json
import time
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import getpass

logger = logging.getLogger(__name__)


@dataclass
class SecurityConfig:
    """安全配置"""
    encryption_enabled: bool = True
    password_min_length: int = 8
    session_timeout: int = 3600  # 1小时
    max_login_attempts: int = 3
    lockout_duration: int = 300  # 5分钟
    audit_enabled: bool = True
    sensitive_fields: List[str] = None
    
    def __post_init__(self):
        if self.sensitive_fields is None:
            self.sensitive_fields = ['password', 'secret', 'key', 'token', 'credential']


@dataclass
class AuditLog:
    """审计日志"""
    timestamp: float
    user: str
    action: str
    resource: str
    success: bool
    details: Dict[str, Any] = None
    ip_address: Optional[str] = None


class EncryptionManager:
    """加密管理器"""
    
    def __init__(self, key_file: str = "encryption.key"):
        self.key_file = Path(key_file)
        self._fernet = None
        self._load_or_create_key()
    
    def _load_or_create_key(self):
        """加载或创建加密密钥"""
        if self.key_file.exists():
            with open(self.key_file, 'rb') as f:
                key = f.read()
        else:
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # 设置文件权限（仅所有者可读写）
            os.chmod(self.key_file, 0o600)
        
        self._fernet = Fernet(key)
    
    def encrypt(self, data: str) -> str:
        """加密数据"""
        if not data:
            return data
        
        encrypted_data = self._fernet.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        if not encrypted_data:
            return encrypted_data
        
        try:
            decoded_data = base64.b64decode(encrypted_data.encode())
            decrypted_data = self._fernet.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            raise ValueError("解密失败")
    
    def encrypt_dict(self, data: Dict[str, Any], sensitive_fields: List[str]) -> Dict[str, Any]:
        """加密字典中的敏感字段"""
        encrypted_data = data.copy()
        
        for key, value in data.items():
            if any(field.lower() in key.lower() for field in sensitive_fields):
                if isinstance(value, str):
                    encrypted_data[key] = self.encrypt(value)
                elif isinstance(value, dict):
                    encrypted_data[key] = self.encrypt_dict(value, sensitive_fields)
        
        return encrypted_data
    
    def decrypt_dict(self, encrypted_data: Dict[str, Any], sensitive_fields: List[str]) -> Dict[str, Any]:
        """解密字典中的敏感字段"""
        decrypted_data = encrypted_data.copy()
        
        for key, value in encrypted_data.items():
            if any(field.lower() in key.lower() for field in sensitive_fields):
                if isinstance(value, str):
                    try:
                        decrypted_data[key] = self.decrypt(value)
                    except ValueError:
                        # 如果解密失败，可能是未加密的数据
                        decrypted_data[key] = value
                elif isinstance(value, dict):
                    decrypted_data[key] = self.decrypt_dict(value, sensitive_fields)
        
        return decrypted_data


class PasswordManager:
    """密码管理器"""
    
    @staticmethod
    def hash_password(password: str, salt: bytes = None) -> tuple:
        """哈希密码"""
        if salt is None:
            salt = secrets.token_bytes(32)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = kdf.derive(password.encode())
        return base64.b64encode(key).decode(), base64.b64encode(salt).decode()
    
    @staticmethod
    def verify_password(password: str, hashed_password: str, salt: str) -> bool:
        """验证密码"""
        try:
            salt_bytes = base64.b64decode(salt.encode())
            expected_hash, _ = PasswordManager.hash_password(password, salt_bytes)
            return expected_hash == hashed_password
        except Exception:
            return False
    
    @staticmethod
    def generate_secure_password(length: int = 16) -> str:
        """生成安全密码"""
        alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    @staticmethod
    def validate_password_strength(password: str, min_length: int = 8) -> tuple:
        """验证密码强度"""
        issues = []
        
        if len(password) < min_length:
            issues.append(f"密码长度至少需要 {min_length} 位")
        
        if not any(c.islower() for c in password):
            issues.append("密码需要包含小写字母")
        
        if not any(c.isupper() for c in password):
            issues.append("密码需要包含大写字母")
        
        if not any(c.isdigit() for c in password):
            issues.append("密码需要包含数字")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("密码需要包含特殊字符")
        
        return len(issues) == 0, issues


class SessionManager:
    """会话管理器"""
    
    def __init__(self, timeout: int = 3600):
        self.timeout = timeout
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.login_attempts: Dict[str, List[float]] = {}
    
    def create_session(self, user: str, ip_address: str = None) -> str:
        """创建会话"""
        session_id = secrets.token_urlsafe(32)
        self.sessions[session_id] = {
            'user': user,
            'created_at': time.time(),
            'last_activity': time.time(),
            'ip_address': ip_address
        }
        return session_id
    
    def validate_session(self, session_id: str) -> bool:
        """验证会话"""
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        current_time = time.time()
        
        # 检查会话是否过期
        if current_time - session['last_activity'] > self.timeout:
            del self.sessions[session_id]
            return False
        
        # 更新最后活动时间
        session['last_activity'] = current_time
        return True
    
    def get_session_user(self, session_id: str) -> Optional[str]:
        """获取会话用户"""
        if self.validate_session(session_id):
            return self.sessions[session_id]['user']
        return None
    
    def destroy_session(self, session_id: str):
        """销毁会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]
    
    def record_login_attempt(self, user: str, success: bool, ip_address: str = None):
        """记录登录尝试"""
        current_time = time.time()
        
        if user not in self.login_attempts:
            self.login_attempts[user] = []
        
        # 清理旧的尝试记录（超过1小时）
        self.login_attempts[user] = [
            attempt for attempt in self.login_attempts[user]
            if current_time - attempt < 3600
        ]
        
        if not success:
            self.login_attempts[user].append(current_time)
    
    def is_user_locked(self, user: str, max_attempts: int = 3, lockout_duration: int = 300) -> bool:
        """检查用户是否被锁定"""
        if user not in self.login_attempts:
            return False
        
        current_time = time.time()
        recent_attempts = [
            attempt for attempt in self.login_attempts[user]
            if current_time - attempt < lockout_duration
        ]
        
        return len(recent_attempts) >= max_attempts


class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self, log_file: str = "audit.log"):
        self.log_file = Path(log_file)
        self.log_file.parent.mkdir(exist_ok=True)
    
    def log_action(self, user: str, action: str, resource: str, success: bool,
                   details: Dict[str, Any] = None, ip_address: str = None):
        """记录操作"""
        audit_log = AuditLog(
            timestamp=time.time(),
            user=user,
            action=action,
            resource=resource,
            success=success,
            details=details,
            ip_address=ip_address
        )
        
        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            log_entry = {
                'timestamp': audit_log.timestamp,
                'user': audit_log.user,
                'action': audit_log.action,
                'resource': audit_log.resource,
                'success': audit_log.success,
                'details': audit_log.details,
                'ip_address': audit_log.ip_address
            }
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        
        # 同时写入系统日志
        log_level = logging.INFO if success else logging.WARNING
        logger.log(log_level, f"审计: {user} {action} {resource} - {'成功' if success else '失败'}")
    
    def get_audit_logs(self, user: str = None, action: str = None, 
                      start_time: float = None, end_time: float = None) -> List[AuditLog]:
        """获取审计日志"""
        logs = []
        
        if not self.log_file.exists():
            return logs
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    log_data = json.loads(line.strip())
                    
                    # 过滤条件
                    if user and log_data.get('user') != user:
                        continue
                    if action and log_data.get('action') != action:
                        continue
                    if start_time and log_data.get('timestamp', 0) < start_time:
                        continue
                    if end_time and log_data.get('timestamp', 0) > end_time:
                        continue
                    
                    logs.append(AuditLog(
                        timestamp=log_data['timestamp'],
                        user=log_data['user'],
                        action=log_data['action'],
                        resource=log_data['resource'],
                        success=log_data['success'],
                        details=log_data.get('details'),
                        ip_address=log_data.get('ip_address')
                    ))
                except json.JSONDecodeError:
                    continue
        
        return logs


class SecurityManager:
    """安全管理器主类"""
    
    def __init__(self, config: SecurityConfig = None):
        self.config = config or SecurityConfig()
        self.encryption_manager = EncryptionManager() if self.config.encryption_enabled else None
        self.session_manager = SessionManager(self.config.session_timeout)
        self.audit_logger = AuditLogger() if self.config.audit_enabled else None
        
        # 用户存储（简化实现，实际应该使用数据库）
        self.users: Dict[str, Dict[str, str]] = {}
        self._load_users()
    
    def _load_users(self):
        """加载用户数据"""
        users_file = Path("users.json")
        if users_file.exists():
            try:
                with open(users_file, 'r', encoding='utf-8') as f:
                    self.users = json.load(f)
            except Exception as e:
                logger.error(f"加载用户数据失败: {str(e)}")
    
    def _save_users(self):
        """保存用户数据"""
        users_file = Path("users.json")
        try:
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, ensure_ascii=False, indent=2)
            os.chmod(users_file, 0o600)
        except Exception as e:
            logger.error(f"保存用户数据失败: {str(e)}")
    
    def create_user(self, username: str, password: str, role: str = "user") -> bool:
        """创建用户"""
        if username in self.users:
            return False
        
        # 验证密码强度
        is_strong, issues = PasswordManager.validate_password_strength(
            password, self.config.password_min_length
        )
        if not is_strong:
            raise ValueError(f"密码不符合要求: {'; '.join(issues)}")
        
        # 哈希密码
        hashed_password, salt = PasswordManager.hash_password(password)
        
        self.users[username] = {
            'password_hash': hashed_password,
            'salt': salt,
            'role': role,
            'created_at': time.time(),
            'last_login': None
        }
        
        self._save_users()
        
        if self.audit_logger:
            self.audit_logger.log_action("system", "create_user", username, True)
        
        return True
    
    def authenticate_user(self, username: str, password: str, ip_address: str = None) -> Optional[str]:
        """用户认证"""
        # 检查用户是否被锁定
        if self.session_manager.is_user_locked(username, self.config.max_login_attempts, self.config.lockout_duration):
            if self.audit_logger:
                self.audit_logger.log_action(username, "login", "system", False, 
                                           {"reason": "account_locked"}, ip_address)
            raise ValueError("账户已被锁定，请稍后再试")
        
        # 验证用户凭据
        if username not in self.users:
            self.session_manager.record_login_attempt(username, False, ip_address)
            if self.audit_logger:
                self.audit_logger.log_action(username, "login", "system", False, 
                                           {"reason": "user_not_found"}, ip_address)
            return None
        
        user_data = self.users[username]
        if not PasswordManager.verify_password(password, user_data['password_hash'], user_data['salt']):
            self.session_manager.record_login_attempt(username, False, ip_address)
            if self.audit_logger:
                self.audit_logger.log_action(username, "login", "system", False, 
                                           {"reason": "invalid_password"}, ip_address)
            return None
        
        # 认证成功
        self.session_manager.record_login_attempt(username, True, ip_address)
        session_id = self.session_manager.create_session(username, ip_address)
        
        # 更新最后登录时间
        self.users[username]['last_login'] = time.time()
        self._save_users()
        
        if self.audit_logger:
            self.audit_logger.log_action(username, "login", "system", True, None, ip_address)
        
        return session_id
    
    def logout_user(self, session_id: str):
        """用户登出"""
        user = self.session_manager.get_session_user(session_id)
        self.session_manager.destroy_session(session_id)
        
        if self.audit_logger and user:
            self.audit_logger.log_action(user, "logout", "system", True)
    
    def encrypt_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """加密配置数据"""
        if not self.encryption_manager:
            return config_data
        
        return self.encryption_manager.encrypt_dict(config_data, self.config.sensitive_fields)
    
    def decrypt_config(self, encrypted_config: Dict[str, Any]) -> Dict[str, Any]:
        """解密配置数据"""
        if not self.encryption_manager:
            return encrypted_config
        
        return self.encryption_manager.decrypt_dict(encrypted_config, self.config.sensitive_fields)
    
    def validate_session(self, session_id: str) -> bool:
        """验证会话"""
        return self.session_manager.validate_session(session_id)
    
    def get_session_user(self, session_id: str) -> Optional[str]:
        """获取会话用户"""
        return self.session_manager.get_session_user(session_id)
    
    def log_action(self, session_id: str, action: str, resource: str, success: bool, 
                   details: Dict[str, Any] = None):
        """记录操作"""
        if not self.audit_logger:
            return
        
        user = self.session_manager.get_session_user(session_id)
        if user:
            session = self.session_manager.sessions.get(session_id, {})
            ip_address = session.get('ip_address')
            self.audit_logger.log_action(user, action, resource, success, details, ip_address)
    
    def get_security_status(self) -> Dict[str, Any]:
        """获取安全状态"""
        return {
            'encryption_enabled': self.config.encryption_enabled,
            'audit_enabled': self.config.audit_enabled,
            'active_sessions': len(self.session_manager.sessions),
            'total_users': len(self.users),
            'locked_users': len([
                user for user in self.users.keys()
                if self.session_manager.is_user_locked(user, self.config.max_login_attempts, self.config.lockout_duration)
            ])
        }


# 全局安全管理器实例
security_manager = SecurityManager()
