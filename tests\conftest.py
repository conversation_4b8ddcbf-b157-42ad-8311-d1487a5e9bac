"""
pytest配置文件和共享fixtures
"""

import pytest
import tempfile
import os
import json
import yaml
from pathlib import Path
from unittest.mock import Mock, MagicMock

# 导入项目模块
from rds_migrate.config import Config<PERSON>anager, DatabaseConfig
from rds_migrate.database import DatabaseManager


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_config_dict():
    """示例配置字典"""
    return {
        "source": {
            "db_type": "mysql",
            "host": "localhost",
            "port": 3306,
            "database": "test_source",
            "username": "test_user",
            "password": "test_pass"
        },
        "target": {
            "db_type": "mysql",
            "host": "localhost",
            "port": 3306,
            "database": "test_target",
            "username": "test_user",
            "password": "test_pass"
        },
        "batch_size": 1000,
        "parallel_workers": 2,
        "tables": ["users", "orders"],
        "exclude_tables": ["temp_table"],
        "schema_only": False,
        "data_only": False,
        "create_indexes": True,
        "create_foreign_keys": True
    }


@pytest.fixture
def sample_yaml_config(temp_dir, sample_config_dict):
    """创建示例YAML配置文件"""
    config_file = temp_dir / "test_config.yaml"
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(sample_config_dict, f, default_flow_style=False, allow_unicode=True)
    return config_file


@pytest.fixture
def sample_json_config(temp_dir, sample_config_dict):
    """创建示例JSON配置文件"""
    config_file = temp_dir / "test_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(sample_config_dict, f, indent=2, ensure_ascii=False)
    return config_file


@pytest.fixture
def mock_database_connection():
    """模拟数据库连接"""
    mock_conn = Mock()
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor
    mock_cursor.fetchall.return_value = []
    mock_cursor.fetchone.return_value = None
    mock_cursor.execute.return_value = None
    return mock_conn


@pytest.fixture
def mock_sqlalchemy_engine():
    """模拟SQLAlchemy引擎"""
    mock_engine = Mock()
    mock_connection = Mock()
    mock_engine.connect.return_value.__enter__.return_value = mock_connection
    mock_connection.execute.return_value.fetchall.return_value = []
    return mock_engine


@pytest.fixture
def database_config():
    """数据库配置对象"""
    return DatabaseConfig(
        db_type="mysql",
        host="localhost",
        port=3306,
        database="test_db",
        username="test_user",
        password="test_pass"
    )


@pytest.fixture
def config_manager():
    """配置管理器"""
    return ConfigManager()


@pytest.fixture
def mock_database_manager(database_config):
    """模拟数据库管理器"""
    manager = DatabaseManager(database_config)
    manager.engine = Mock()
    manager.connection = Mock()
    return manager


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )


# 测试环境设置
@pytest.fixture(autouse=True)
def setup_test_environment():
    """设置测试环境"""
    # 设置测试环境变量
    os.environ['TESTING'] = 'true'
    
    yield
    
    # 清理测试环境
    if 'TESTING' in os.environ:
        del os.environ['TESTING']
