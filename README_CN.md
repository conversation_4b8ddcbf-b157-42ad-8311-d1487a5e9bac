# RDS数据库迁移工具

一个功能强大且灵活的数据库迁移工具，支持传统数据库与云RDS实例之间的数据迁移，以及不同RDS提供商之间的迁移。

## 🚀 主要特性

- **多数据库支持**: MySQL, PostgreSQL, SQL Server, Oracle
- **灵活的迁移场景**:
  - 传统数据库 → 云RDS
  - RDS → RDS（相同或不同提供商）
  - RDS → 传统数据库
- **高性能**: 多线程并行处理，可配置批次大小
- **进度监控**: 实时进度跟踪，彩色控制台输出
- **配置管理**: 基于YAML的配置，支持验证
- **错误处理**: 全面的错误处理和回滚功能
- **多种界面**: 命令行、图形界面、Web界面
- **干运行支持**: 测试迁移而不实际执行更改

## 🎯 支持的迁移场景

### 1. 传统数据库到云RDS
- 本地MySQL → AWS RDS MySQL
- 本地PostgreSQL → Azure Database for PostgreSQL
- 本地SQL Server → AWS RDS SQL Server

### 2. RDS间迁移
- AWS RDS → Azure Database
- Heroku Postgres → AWS RDS PostgreSQL
- 跨区域RDS迁移

### 3. 跨数据库引擎迁移
- MySQL → PostgreSQL
- SQL Server → MySQL
- Oracle → PostgreSQL

## 📦 安装

1. 克隆仓库:
```bash
git clone <repository-url>
cd rds-migrate
```

2. 安装依赖:
```bash
pip install -r requirements.txt
```

3. Oracle支持（可选）:
```bash
# 首先安装Oracle Instant Client
pip install cx-Oracle
```

## 🚀 快速开始

### 方式一：Web界面（推荐）
```bash
python run_web.py
```
然后在浏览器中访问 http://localhost:8000

### 方式二：图形界面
```bash
python run_gui.py
```

### 方式三：命令行界面

#### 1. 创建配置
```bash
python main.py init
```

#### 2. 测试连接
```bash
python main.py test -c config.yaml
```

#### 3. 执行迁移
```bash
python main.py migrate -c config.yaml
```

## ⚙️ 配置文件

工具使用YAML配置文件。基本示例：

```yaml
source:
  host: "source-db.example.com"
  port: 3306
  database: "source_db"
  username: "user"
  password: "password"
  db_type: "mysql"

target:
  host: "target-rds.amazonaws.com"
  port: 3306
  database: "target_db"
  username: "admin"
  password: "password"
  db_type: "mysql"

migration:
  batch_size: 1000
  parallel_workers: 4
  create_indexes: true
  create_foreign_keys: true
```

## 🖥️ 命令行使用

### 初始化配置
```bash
python main.py init [--output config.yaml]
```

### 验证配置
```bash
python main.py validate -c config.yaml
```

### 测试数据库连接
```bash
python main.py test -c config.yaml
```

### 分析源数据库
```bash
python main.py analyze -c config.yaml [--output analysis.txt]
```

### 执行迁移
```bash
python main.py migrate -c config.yaml [选项]

选项:
  --dry-run          测试迁移而不实际执行
  --log-level LEVEL  设置日志级别 (DEBUG, INFO, WARNING, ERROR)
  --report FILE      生成迁移报告
  --parallel N       并行工作线程数
  --batch-size N     数据迁移批次大小
```

### 交互模式
```bash
python main.py interactive
```

## 📁 配置示例

查看 `examples/` 目录中的示例配置：

- `mysql_to_aws_rds.yaml` - MySQL到AWS RDS迁移
- `postgres_to_postgres.yaml` - PostgreSQL到PostgreSQL迁移
- `mysql_to_postgresql.yaml` - 跨数据库迁移
- `schema_only_migration.yaml` - 仅结构迁移
- `data_only_migration.yaml` - 仅数据迁移

## ⚡ 性能调优

### 批次大小
- 小型数据库: 500-1000 行/批次
- 大型数据库: 5000-10000 行/批次
- 超大表: 10000+ 行/批次

### 并行工作线程
- 本地网络: 2-4 个工作线程
- 云迁移: 4-8 个工作线程
- 高带宽: 8-16 个工作线程

### 内存考虑
- 监控大型迁移期间的内存使用
- 如果内存使用过高，减少批次大小
- 使用 `--parallel` 限制并发连接

## 🔧 故障排除

### 连接问题
1. 验证网络连接
2. 检查防火墙设置
3. 验证凭据
4. 测试较小的超时值

### 性能问题
1. 减少批次大小
2. 减少并行工作线程
3. 检查网络带宽
4. 监控数据库性能

### 数据完整性
1. 迁移前始终备份目标数据库
2. 使用干运行模式进行测试
3. 迁移后验证行数
4. 检查数据类型兼容性

## 🔒 安全考虑

- 将密码存储在环境变量中
- 可用时使用SSL/TLS连接
- 限制数据库用户权限
- 监控迁移日志中的敏感数据

## 🤝 贡献

1. Fork仓库
2. 创建功能分支
3. 进行更改
4. 如适用，添加测试
5. 提交拉取请求

## 📄 许可证

本项目采用MIT许可证 - 详见LICENSE文件。

## 🆘 支持

如有问题和疑问：
1. 查看故障排除部分
2. 查看现有问题
3. 创建包含详细信息的新问题

## 📝 更新日志

### 版本 1.0.0
- 初始发布
- 支持MySQL, PostgreSQL, SQL Server, Oracle
- 多线程迁移引擎
- CLI界面和交互模式
- 配置验证和测试
- 进度监控和报告
- 新增Web界面和GUI界面
- 异步高性能迁移引擎

## 🌟 界面预览

### Web界面
现代化的Web界面，支持实时进度监控和WebSocket通信：
- 直观的配置管理
- 实时迁移进度
- 详细的日志监控
- 响应式设计

### 图形界面
基于Tkinter的桌面应用：
- 用户友好的GUI
- 配置向导
- 进度可视化
- 错误处理

### 命令行界面
功能完整的CLI工具：
- 交互式配置
- 彩色输出
- 详细日志
- 批处理支持
