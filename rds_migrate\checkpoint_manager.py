"""
断点续传管理器 - 支持迁移中断后的可靠恢复
"""

import json
import time
import hashlib
import sqlite3
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


@dataclass
class BatchCheckpoint:
    """批次检查点"""
    table_name: str
    batch_id: int
    offset: int
    batch_size: int
    status: str  # 'pending', 'processing', 'completed', 'failed'
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    rows_processed: int = 0
    error_message: Optional[str] = None
    retry_count: int = 0
    checksum: Optional[str] = None


@dataclass
class TableCheckpoint:
    """表检查点"""
    table_name: str
    total_rows: int
    processed_rows: int
    total_batches: int
    completed_batches: int
    failed_batches: int
    status: str  # 'pending', 'processing', 'completed', 'failed', 'paused'
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    schema_migrated: bool = False
    data_verified: bool = False
    last_checkpoint: float = 0


@dataclass
class MigrationSession:
    """迁移会话"""
    session_id: str
    migration_name: str
    source_config_hash: str
    target_config_hash: str
    migration_config: Dict[str, Any]
    status: str  # 'active', 'paused', 'completed', 'failed', 'cancelled'
    created_at: float
    updated_at: float
    total_tables: int = 0
    completed_tables: int = 0
    total_rows: int = 0
    processed_rows: int = 0


class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.db_path = self.checkpoint_dir / "migration_checkpoints.db"
        self._init_database()
    
    def _init_database(self):
        """初始化检查点数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS migration_sessions (
                    session_id TEXT PRIMARY KEY,
                    migration_name TEXT NOT NULL,
                    source_config_hash TEXT NOT NULL,
                    target_config_hash TEXT NOT NULL,
                    migration_config TEXT NOT NULL,
                    status TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    updated_at REAL NOT NULL,
                    total_tables INTEGER DEFAULT 0,
                    completed_tables INTEGER DEFAULT 0,
                    total_rows INTEGER DEFAULT 0,
                    processed_rows INTEGER DEFAULT 0
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS table_checkpoints (
                    session_id TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    total_rows INTEGER NOT NULL,
                    processed_rows INTEGER DEFAULT 0,
                    total_batches INTEGER NOT NULL,
                    completed_batches INTEGER DEFAULT 0,
                    failed_batches INTEGER DEFAULT 0,
                    status TEXT NOT NULL,
                    start_time REAL,
                    end_time REAL,
                    schema_migrated BOOLEAN DEFAULT FALSE,
                    data_verified BOOLEAN DEFAULT FALSE,
                    last_checkpoint REAL DEFAULT 0,
                    PRIMARY KEY (session_id, table_name),
                    FOREIGN KEY (session_id) REFERENCES migration_sessions(session_id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS batch_checkpoints (
                    session_id TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    batch_id INTEGER NOT NULL,
                    offset_value INTEGER NOT NULL,
                    batch_size INTEGER NOT NULL,
                    status TEXT NOT NULL,
                    start_time REAL,
                    end_time REAL,
                    rows_processed INTEGER DEFAULT 0,
                    error_message TEXT,
                    retry_count INTEGER DEFAULT 0,
                    checksum TEXT,
                    PRIMARY KEY (session_id, table_name, batch_id),
                    FOREIGN KEY (session_id, table_name) REFERENCES table_checkpoints(session_id, table_name)
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_status ON migration_sessions(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_tables_status ON table_checkpoints(session_id, status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_batches_status ON batch_checkpoints(session_id, table_name, status)")
    
    def create_session(self, migration_name: str, source_config: Dict, target_config: Dict, 
                      migration_config: Dict) -> str:
        """创建迁移会话"""
        session_id = self._generate_session_id(migration_name)
        source_hash = self._hash_config(source_config)
        target_hash = self._hash_config(target_config)
        
        session = MigrationSession(
            session_id=session_id,
            migration_name=migration_name,
            source_config_hash=source_hash,
            target_config_hash=target_hash,
            migration_config=migration_config,
            status='active',
            created_at=time.time(),
            updated_at=time.time()
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO migration_sessions 
                (session_id, migration_name, source_config_hash, target_config_hash, 
                 migration_config, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session.session_id, session.migration_name, session.source_config_hash,
                session.target_config_hash, json.dumps(session.migration_config),
                session.status, session.created_at, session.updated_at
            ))
        
        logger.info(f"创建迁移会话: {session_id}")
        return session_id
    
    def find_resumable_session(self, migration_name: str, source_config: Dict, 
                             target_config: Dict) -> Optional[str]:
        """查找可恢复的会话"""
        source_hash = self._hash_config(source_config)
        target_hash = self._hash_config(target_config)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT session_id FROM migration_sessions 
                WHERE migration_name = ? AND source_config_hash = ? AND target_config_hash = ?
                AND status IN ('active', 'paused')
                ORDER BY updated_at DESC LIMIT 1
            """, (migration_name, source_hash, target_hash))
            
            result = cursor.fetchone()
            return result[0] if result else None
    
    def get_session(self, session_id: str) -> Optional[MigrationSession]:
        """获取会话信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM migration_sessions WHERE session_id = ?
            """, (session_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            return MigrationSession(
                session_id=row[0],
                migration_name=row[1],
                source_config_hash=row[2],
                target_config_hash=row[3],
                migration_config=json.loads(row[4]),
                status=row[5],
                created_at=row[6],
                updated_at=row[7],
                total_tables=row[8],
                completed_tables=row[9],
                total_rows=row[10],
                processed_rows=row[11]
            )
    
    def update_session_status(self, session_id: str, status: str, **kwargs):
        """更新会话状态"""
        update_fields = ['status = ?', 'updated_at = ?']
        values = [status, time.time()]
        
        for key, value in kwargs.items():
            if key in ['total_tables', 'completed_tables', 'total_rows', 'processed_rows']:
                update_fields.append(f'{key} = ?')
                values.append(value)
        
        values.append(session_id)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(f"""
                UPDATE migration_sessions 
                SET {', '.join(update_fields)}
                WHERE session_id = ?
            """, values)
    
    def save_table_checkpoint(self, session_id: str, table_checkpoint: TableCheckpoint):
        """保存表检查点"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO table_checkpoints 
                (session_id, table_name, total_rows, processed_rows, total_batches,
                 completed_batches, failed_batches, status, start_time, end_time,
                 schema_migrated, data_verified, last_checkpoint)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session_id, table_checkpoint.table_name, table_checkpoint.total_rows,
                table_checkpoint.processed_rows, table_checkpoint.total_batches,
                table_checkpoint.completed_batches, table_checkpoint.failed_batches,
                table_checkpoint.status, table_checkpoint.start_time, table_checkpoint.end_time,
                table_checkpoint.schema_migrated, table_checkpoint.data_verified,
                table_checkpoint.last_checkpoint
            ))
    
    def get_table_checkpoint(self, session_id: str, table_name: str) -> Optional[TableCheckpoint]:
        """获取表检查点"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM table_checkpoints 
                WHERE session_id = ? AND table_name = ?
            """, (session_id, table_name))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            return TableCheckpoint(
                table_name=row[1],
                total_rows=row[2],
                processed_rows=row[3],
                total_batches=row[4],
                completed_batches=row[5],
                failed_batches=row[6],
                status=row[7],
                start_time=row[8],
                end_time=row[9],
                schema_migrated=bool(row[10]),
                data_verified=bool(row[11]),
                last_checkpoint=row[12]
            )
    
    def get_session_tables(self, session_id: str) -> List[TableCheckpoint]:
        """获取会话的所有表检查点"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM table_checkpoints WHERE session_id = ?
                ORDER BY table_name
            """, (session_id,))
            
            tables = []
            for row in cursor.fetchall():
                tables.append(TableCheckpoint(
                    table_name=row[1],
                    total_rows=row[2],
                    processed_rows=row[3],
                    total_batches=row[4],
                    completed_batches=row[5],
                    failed_batches=row[6],
                    status=row[7],
                    start_time=row[8],
                    end_time=row[9],
                    schema_migrated=bool(row[10]),
                    data_verified=bool(row[11]),
                    last_checkpoint=row[12]
                ))
            
            return tables
    
    def save_batch_checkpoint(self, session_id: str, batch_checkpoint: BatchCheckpoint):
        """保存批次检查点"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO batch_checkpoints 
                (session_id, table_name, batch_id, offset_value, batch_size, status,
                 start_time, end_time, rows_processed, error_message, retry_count, checksum)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session_id, batch_checkpoint.table_name, batch_checkpoint.batch_id,
                batch_checkpoint.offset, batch_checkpoint.batch_size, batch_checkpoint.status,
                batch_checkpoint.start_time, batch_checkpoint.end_time,
                batch_checkpoint.rows_processed, batch_checkpoint.error_message,
                batch_checkpoint.retry_count, batch_checkpoint.checksum
            ))
    
    def get_pending_batches(self, session_id: str, table_name: str) -> List[BatchCheckpoint]:
        """获取待处理的批次"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM batch_checkpoints 
                WHERE session_id = ? AND table_name = ? AND status IN ('pending', 'failed')
                ORDER BY batch_id
            """, (session_id, table_name))
            
            batches = []
            for row in cursor.fetchall():
                batches.append(BatchCheckpoint(
                    table_name=row[1],
                    batch_id=row[2],
                    offset=row[3],
                    batch_size=row[4],
                    status=row[5],
                    start_time=row[6],
                    end_time=row[7],
                    rows_processed=row[8],
                    error_message=row[9],
                    retry_count=row[10],
                    checksum=row[11]
                ))
            
            return batches
    
    def get_completed_batches(self, session_id: str, table_name: str) -> Set[int]:
        """获取已完成的批次ID集合"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT batch_id FROM batch_checkpoints 
                WHERE session_id = ? AND table_name = ? AND status = 'completed'
            """, (session_id, table_name))
            
            return {row[0] for row in cursor.fetchall()}
    
    def cleanup_old_sessions(self, days: int = 30):
        """清理旧的会话数据"""
        cutoff_time = time.time() - (days * 24 * 3600)
        
        with sqlite3.connect(self.db_path) as conn:
            # 删除旧的已完成或失败的会话
            cursor = conn.execute("""
                SELECT session_id FROM migration_sessions 
                WHERE status IN ('completed', 'failed', 'cancelled') 
                AND updated_at < ?
            """, (cutoff_time,))
            
            old_sessions = [row[0] for row in cursor.fetchall()]
            
            for session_id in old_sessions:
                self.delete_session(session_id)
            
            logger.info(f"清理了 {len(old_sessions)} 个旧会话")
    
    def delete_session(self, session_id: str):
        """删除会话及其所有数据"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("DELETE FROM batch_checkpoints WHERE session_id = ?", (session_id,))
            conn.execute("DELETE FROM table_checkpoints WHERE session_id = ?", (session_id,))
            conn.execute("DELETE FROM migration_sessions WHERE session_id = ?", (session_id,))
        
        logger.info(f"删除会话: {session_id}")
    
    def get_session_progress(self, session_id: str) -> Dict[str, Any]:
        """获取会话进度信息"""
        session = self.get_session(session_id)
        if not session:
            return {}
        
        tables = self.get_session_tables(session_id)
        
        total_rows = sum(t.total_rows for t in tables)
        processed_rows = sum(t.processed_rows for t in tables)
        
        progress = {
            'session_id': session_id,
            'status': session.status,
            'total_tables': len(tables),
            'completed_tables': len([t for t in tables if t.status == 'completed']),
            'total_rows': total_rows,
            'processed_rows': processed_rows,
            'progress_percentage': (processed_rows / total_rows * 100) if total_rows > 0 else 0,
            'created_at': session.created_at,
            'updated_at': session.updated_at,
            'tables': [
                {
                    'name': t.table_name,
                    'status': t.status,
                    'total_rows': t.total_rows,
                    'processed_rows': t.processed_rows,
                    'progress': (t.processed_rows / t.total_rows * 100) if t.total_rows > 0 else 0,
                    'completed_batches': t.completed_batches,
                    'failed_batches': t.failed_batches
                }
                for t in tables
            ]
        }
        
        return progress
    
    def _generate_session_id(self, migration_name: str) -> str:
        """生成会话ID"""
        timestamp = str(time.time())
        content = f"{migration_name}:{timestamp}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _hash_config(self, config: Dict) -> str:
        """计算配置哈希"""
        config_str = json.dumps(config, sort_keys=True)
        return hashlib.sha256(config_str.encode()).hexdigest()
    
    def list_sessions(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出会话"""
        with sqlite3.connect(self.db_path) as conn:
            if status:
                cursor = conn.execute("""
                    SELECT session_id, migration_name, status, created_at, updated_at,
                           total_tables, completed_tables, total_rows, processed_rows
                    FROM migration_sessions WHERE status = ?
                    ORDER BY updated_at DESC
                """, (status,))
            else:
                cursor = conn.execute("""
                    SELECT session_id, migration_name, status, created_at, updated_at,
                           total_tables, completed_tables, total_rows, processed_rows
                    FROM migration_sessions
                    ORDER BY updated_at DESC
                """)
            
            sessions = []
            for row in cursor.fetchall():
                sessions.append({
                    'session_id': row[0],
                    'migration_name': row[1],
                    'status': row[2],
                    'created_at': row[3],
                    'updated_at': row[4],
                    'total_tables': row[5],
                    'completed_tables': row[6],
                    'total_rows': row[7],
                    'processed_rows': row[8],
                    'progress': (row[8] / row[7] * 100) if row[7] > 0 else 0
                })
            
            return sessions
