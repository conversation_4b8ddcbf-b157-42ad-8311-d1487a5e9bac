"""
性能监控系统 - 实时性能监控、指标收集和性能分析
优化版本包含增强的类型安全、内存管理和异步性能监控
"""

import time
import asyncio
import threading
import logging
import gc
import sys
from typing import Dict, List, Optional, Any, Callable, Union, Protocol, TypedDict
from dataclasses import dataclass, field
from collections import deque, defaultdict
from datetime import datetime, timedelta
from enum import Enum, auto
import json
import sqlite3
from pathlib import Path
from contextlib import asynccontextmanager
import weakref

# 可选依赖导入
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    psutil = None
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available, system metrics will be limited")

try:
    import prometheus_client
    PROMETHEUS_AVAILABLE = True
except ImportError:
    prometheus_client = None
    PROMETHEUS_AVAILABLE = False

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = auto()
    GAUGE = auto()
    HISTOGRAM = auto()
    SUMMARY = auto()


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class PerformanceMetrics:
    """
    性能指标，增强版本包含更多系统和应用指标
    """
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    disk_usage_percent: float
    network_sent_mb: float
    network_recv_mb: float
    active_connections: int
    queries_per_second: float
    avg_query_time: float
    min_query_time: float
    max_query_time: float
    error_rate: float
    throughput_rows_per_sec: float
    gc_collections: int
    thread_count: int
    open_files: int

    def to_dict(self) -> Dict[str, Union[float, int]]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp,
            'cpu_percent': self.cpu_percent,
            'memory_percent': self.memory_percent,
            'memory_used_mb': self.memory_used_mb,
            'memory_available_mb': self.memory_available_mb,
            'disk_io_read_mb': self.disk_io_read_mb,
            'disk_io_write_mb': self.disk_io_write_mb,
            'disk_usage_percent': self.disk_usage_percent,
            'network_sent_mb': self.network_sent_mb,
            'network_recv_mb': self.network_recv_mb,
            'active_connections': self.active_connections,
            'queries_per_second': self.queries_per_second,
            'avg_query_time': self.avg_query_time,
            'min_query_time': self.min_query_time,
            'max_query_time': self.max_query_time,
            'error_rate': self.error_rate,
            'throughput_rows_per_sec': self.throughput_rows_per_sec,
            'gc_collections': self.gc_collections,
            'thread_count': self.thread_count,
            'open_files': self.open_files
        }


@dataclass
class TableMetrics:
    """
    表级别指标，增强版本包含更详细的统计信息
    """
    table_name: str
    rows_processed: int
    rows_per_second: float
    avg_batch_time: float
    min_batch_time: float
    max_batch_time: float
    error_count: int
    success_count: int
    last_update: float
    total_time: float
    memory_usage_mb: float

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        total = self.success_count + self.error_count
        if total == 0:
            return 100.0
        return (self.success_count / total) * 100.0

    @property
    def efficiency_score(self) -> float:
        """计算效率分数（基于速度和成功率）"""
        return self.rows_per_second * (self.success_rate / 100.0)


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric_name: str
    threshold: float
    comparison: str  # '>', '<', '>=', '<=', '=='
    level: AlertLevel
    enabled: bool = True
    cooldown_seconds: int = 300  # 5分钟冷却期
    last_triggered: Optional[float] = None

    def should_trigger(self, value: float) -> bool:
        """检查是否应该触发告警"""
        if not self.enabled:
            return False

        # 检查冷却期
        if (self.last_triggered and
            time.time() - self.last_triggered < self.cooldown_seconds):
            return False

        # 检查阈值
        if self.comparison == '>':
            return value > self.threshold
        elif self.comparison == '<':
            return value < self.threshold
        elif self.comparison == '>=':
            return value >= self.threshold
        elif self.comparison == '<=':
            return value <= self.threshold
        elif self.comparison == '==':
            return abs(value - self.threshold) < 0.001

        return False


class MetricsCollectorProtocol(Protocol):
    """指标收集器协议"""
    async def collect_metrics(self) -> PerformanceMetrics: ...
    async def start_collection(self) -> None: ...
    async def stop_collection(self) -> None: ...


@dataclass
class ConnectionMetrics:
    """连接指标"""
    connection_id: str
    host: str
    database: str
    active_time: float
    query_count: int
    avg_response_time: float
    error_count: int
    last_activity: float


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, collection_interval: float = 1.0):
        self.collection_interval = collection_interval
        self.metrics_history: deque = deque(maxlen=3600)  # 保留1小时数据
        self.table_metrics: Dict[str, TableMetrics] = {}
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        
        # 系统指标基线
        self._last_disk_io = psutil.disk_io_counters()
        self._last_network_io = psutil.net_io_counters()
        self._last_collection_time = time.time()
        
        # 查询统计
        self._query_times: deque = deque(maxlen=1000)
        self._error_count = 0
        self._total_queries = 0
        
        # 控制标志
        self._collecting = False
        self._collection_task: Optional[asyncio.Task] = None
    
    async def start_collection(self):
        """开始收集指标"""
        if self._collecting:
            return
        
        self._collecting = True
        self._collection_task = asyncio.create_task(self._collection_loop())
        logger.info("性能指标收集已启动")
    
    async def stop_collection(self):
        """停止收集指标"""
        self._collecting = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        logger.info("性能指标收集已停止")
    
    async def _collection_loop(self):
        """指标收集循环"""
        while self._collecting:
            try:
                metrics = self._collect_system_metrics()
                self.metrics_history.append(metrics)
                await asyncio.sleep(self.collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"收集指标时出错: {str(e)}")
                await asyncio.sleep(self.collection_interval)
    
    def _collect_system_metrics(self) -> PerformanceMetrics:
        """收集系统指标"""
        current_time = time.time()
        
        # CPU和内存
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / 1024 / 1024
        
        # 磁盘IO
        current_disk_io = psutil.disk_io_counters()
        time_delta = current_time - self._last_collection_time
        
        if time_delta > 0 and self._last_disk_io:
            disk_read_mb = (current_disk_io.read_bytes - self._last_disk_io.read_bytes) / 1024 / 1024 / time_delta
            disk_write_mb = (current_disk_io.write_bytes - self._last_disk_io.write_bytes) / 1024 / 1024 / time_delta
        else:
            disk_read_mb = disk_write_mb = 0
        
        # 网络IO
        current_network_io = psutil.net_io_counters()
        if time_delta > 0 and self._last_network_io:
            network_sent_mb = (current_network_io.bytes_sent - self._last_network_io.bytes_sent) / 1024 / 1024 / time_delta
            network_recv_mb = (current_network_io.bytes_recv - self._last_network_io.bytes_recv) / 1024 / 1024 / time_delta
        else:
            network_sent_mb = network_recv_mb = 0
        
        # 查询统计
        queries_per_second = len(self._query_times) / min(60, current_time - (self._query_times[0] if self._query_times else current_time))
        avg_query_time = sum(self._query_times) / len(self._query_times) if self._query_times else 0
        error_rate = (self._error_count / max(self._total_queries, 1)) * 100
        
        # 吞吐量计算
        throughput = sum(
            metrics.rows_per_second for metrics in self.table_metrics.values()
            if current_time - metrics.last_update < 60
        )
        
        # 更新基线
        self._last_disk_io = current_disk_io
        self._last_network_io = current_network_io
        self._last_collection_time = current_time
        
        return PerformanceMetrics(
            timestamp=current_time,
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb,
            active_connections=len(self.connection_metrics),
            queries_per_second=queries_per_second,
            avg_query_time=avg_query_time,
            error_rate=error_rate,
            throughput_rows_per_sec=throughput
        )
    
    def record_query(self, query_time: float, success: bool = True):
        """记录查询"""
        self._query_times.append(query_time)
        self._total_queries += 1
        if not success:
            self._error_count += 1
    
    def update_table_metrics(self, table_name: str, rows_processed: int, batch_time: float, error_count: int = 0):
        """更新表指标"""
        current_time = time.time()
        
        if table_name in self.table_metrics:
            metrics = self.table_metrics[table_name]
            time_delta = current_time - metrics.last_update
            if time_delta > 0:
                metrics.rows_per_second = rows_processed / time_delta
            metrics.rows_processed += rows_processed
            metrics.avg_batch_time = (metrics.avg_batch_time + batch_time) / 2
            metrics.error_count += error_count
            metrics.last_update = current_time
        else:
            self.table_metrics[table_name] = TableMetrics(
                table_name=table_name,
                rows_processed=rows_processed,
                rows_per_second=rows_processed,
                avg_batch_time=batch_time,
                error_count=error_count,
                last_update=current_time
            )
    
    def update_connection_metrics(self, connection_id: str, host: str, database: str, 
                                query_time: float, success: bool = True):
        """更新连接指标"""
        current_time = time.time()
        
        if connection_id in self.connection_metrics:
            metrics = self.connection_metrics[connection_id]
            metrics.query_count += 1
            metrics.avg_response_time = (metrics.avg_response_time + query_time) / 2
            if not success:
                metrics.error_count += 1
            metrics.last_activity = current_time
        else:
            self.connection_metrics[connection_id] = ConnectionMetrics(
                connection_id=connection_id,
                host=host,
                database=database,
                active_time=current_time,
                query_count=1,
                avg_response_time=query_time,
                error_count=0 if success else 1,
                last_activity=current_time
            )
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前指标"""
        return self.metrics_history[-1] if self.metrics_history else None
    
    def get_metrics_history(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """获取指标历史"""
        cutoff_time = time.time() - (minutes * 60)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_table_metrics(self) -> Dict[str, TableMetrics]:
        """获取表指标"""
        return self.table_metrics.copy()
    
    def get_connection_metrics(self) -> Dict[str, ConnectionMetrics]:
        """获取连接指标"""
        return self.connection_metrics.copy()


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, collector: MetricsCollector):
        self.collector = collector
    
    def analyze_performance(self, minutes: int = 60) -> Dict[str, Any]:
        """分析性能"""
        metrics_history = self.collector.get_metrics_history(minutes)
        if not metrics_history:
            return {}
        
        # 基础统计
        cpu_values = [m.cpu_percent for m in metrics_history]
        memory_values = [m.memory_percent for m in metrics_history]
        query_times = [m.avg_query_time for m in metrics_history]
        throughput_values = [m.throughput_rows_per_sec for m in metrics_history]
        
        analysis = {
            'time_range_minutes': minutes,
            'sample_count': len(metrics_history),
            'cpu_stats': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory_stats': {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            },
            'query_performance': {
                'avg_time': sum(query_times) / len(query_times),
                'max_time': max(query_times),
                'min_time': min(query_times)
            },
            'throughput_stats': {
                'avg': sum(throughput_values) / len(throughput_values),
                'max': max(throughput_values),
                'min': min(throughput_values)
            }
        }
        
        # 性能趋势分析
        if len(metrics_history) >= 10:
            analysis['trends'] = self._analyze_trends(metrics_history)
        
        # 瓶颈检测
        analysis['bottlenecks'] = self._detect_bottlenecks(metrics_history)
        
        # 表性能分析
        analysis['table_performance'] = self._analyze_table_performance()
        
        return analysis
    
    def _analyze_trends(self, metrics_history: List[PerformanceMetrics]) -> Dict[str, str]:
        """分析性能趋势"""
        trends = {}
        
        # 分析CPU趋势
        cpu_values = [m.cpu_percent for m in metrics_history]
        cpu_trend = self._calculate_trend(cpu_values)
        trends['cpu'] = cpu_trend
        
        # 分析内存趋势
        memory_values = [m.memory_percent for m in metrics_history]
        memory_trend = self._calculate_trend(memory_values)
        trends['memory'] = memory_trend
        
        # 分析查询时间趋势
        query_times = [m.avg_query_time for m in metrics_history]
        query_trend = self._calculate_trend(query_times)
        trends['query_time'] = query_trend
        
        return trends
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 2:
            return 'stable'
        
        # 简单线性回归斜率
        n = len(values)
        x_sum = sum(range(n))
        y_sum = sum(values)
        xy_sum = sum(i * values[i] for i in range(n))
        x2_sum = sum(i * i for i in range(n))
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        
        if slope > 0.1:
            return 'increasing'
        elif slope < -0.1:
            return 'decreasing'
        else:
            return 'stable'
    
    def _detect_bottlenecks(self, metrics_history: List[PerformanceMetrics]) -> List[str]:
        """检测性能瓶颈"""
        bottlenecks = []
        
        if not metrics_history:
            return bottlenecks
        
        latest = metrics_history[-1]
        
        # CPU瓶颈
        if latest.cpu_percent > 80:
            bottlenecks.append('high_cpu_usage')
        
        # 内存瓶颈
        if latest.memory_percent > 85:
            bottlenecks.append('high_memory_usage')
        
        # 查询时间瓶颈
        if latest.avg_query_time > 5.0:
            bottlenecks.append('slow_queries')
        
        # 错误率瓶颈
        if latest.error_rate > 5.0:
            bottlenecks.append('high_error_rate')
        
        # 磁盘IO瓶颈
        if latest.disk_io_read_mb + latest.disk_io_write_mb > 100:
            bottlenecks.append('high_disk_io')
        
        return bottlenecks
    
    def _analyze_table_performance(self) -> Dict[str, Any]:
        """分析表性能"""
        table_metrics = self.collector.get_table_metrics()
        
        if not table_metrics:
            return {}
        
        # 按性能排序
        sorted_tables = sorted(
            table_metrics.values(),
            key=lambda x: x.rows_per_second,
            reverse=True
        )
        
        analysis = {
            'total_tables': len(table_metrics),
            'fastest_table': {
                'name': sorted_tables[0].table_name,
                'rows_per_second': sorted_tables[0].rows_per_second
            } if sorted_tables else None,
            'slowest_table': {
                'name': sorted_tables[-1].table_name,
                'rows_per_second': sorted_tables[-1].rows_per_second
            } if sorted_tables else None,
            'tables_with_errors': [
                {
                    'name': t.table_name,
                    'error_count': t.error_count
                }
                for t in table_metrics.values()
                if t.error_count > 0
            ]
        }
        
        return analysis
    
    def generate_performance_report(self, minutes: int = 60) -> str:
        """生成性能报告"""
        analysis = self.analyze_performance(minutes)
        
        if not analysis:
            return "无性能数据可用"
        
        report = f"""
性能分析报告 (最近 {minutes} 分钟)
{'=' * 50}

系统资源使用:
- CPU平均使用率: {analysis['cpu_stats']['avg']:.1f}% (最高: {analysis['cpu_stats']['max']:.1f}%)
- 内存平均使用率: {analysis['memory_stats']['avg']:.1f}% (最高: {analysis['memory_stats']['max']:.1f}%)

查询性能:
- 平均查询时间: {analysis['query_performance']['avg_time']:.3f}秒
- 最慢查询时间: {analysis['query_performance']['max_time']:.3f}秒

数据吞吐量:
- 平均处理速度: {analysis['throughput_stats']['avg']:.0f} 行/秒
- 峰值处理速度: {analysis['throughput_stats']['max']:.0f} 行/秒
"""
        
        # 添加瓶颈信息
        if analysis.get('bottlenecks'):
            report += f"\n检测到的性能瓶颈:\n"
            for bottleneck in analysis['bottlenecks']:
                report += f"- {bottleneck}\n"
        
        # 添加表性能信息
        table_perf = analysis.get('table_performance', {})
        if table_perf:
            report += f"\n表性能统计:\n"
            report += f"- 总表数: {table_perf['total_tables']}\n"
            
            if table_perf.get('fastest_table'):
                fastest = table_perf['fastest_table']
                report += f"- 最快表: {fastest['name']} ({fastest['rows_per_second']:.0f} 行/秒)\n"
            
            if table_perf.get('slowest_table'):
                slowest = table_perf['slowest_table']
                report += f"- 最慢表: {slowest['name']} ({slowest['rows_per_second']:.0f} 行/秒)\n"
            
            if table_perf.get('tables_with_errors'):
                report += f"- 有错误的表: {len(table_perf['tables_with_errors'])} 个\n"
        
        return report


class PerformanceMonitor:
    """性能监控器主类"""
    
    def __init__(self, collection_interval: float = 1.0, storage_path: str = "performance_data"):
        self.collector = MetricsCollector(collection_interval)
        self.analyzer = PerformanceAnalyzer(self.collector)
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        self.threshold_config = {
            'cpu_threshold': 80.0,
            'memory_threshold': 85.0,
            'query_time_threshold': 5.0,
            'error_rate_threshold': 5.0
        }
    
    async def start_monitoring(self):
        """开始监控"""
        await self.collector.start_collection()
        logger.info("性能监控已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        await self.collector.stop_collection()
        logger.info("性能监控已停止")
    
    def add_alert_callback(self, callback: Callable):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def check_alerts(self):
        """检查告警"""
        current_metrics = self.collector.get_current_metrics()
        if not current_metrics:
            return
        
        alerts = []
        
        if current_metrics.cpu_percent > self.threshold_config['cpu_threshold']:
            alerts.append(f"CPU使用率过高: {current_metrics.cpu_percent:.1f}%")
        
        if current_metrics.memory_percent > self.threshold_config['memory_threshold']:
            alerts.append(f"内存使用率过高: {current_metrics.memory_percent:.1f}%")
        
        if current_metrics.avg_query_time > self.threshold_config['query_time_threshold']:
            alerts.append(f"查询时间过长: {current_metrics.avg_query_time:.3f}秒")
        
        if current_metrics.error_rate > self.threshold_config['error_rate_threshold']:
            alerts.append(f"错误率过高: {current_metrics.error_rate:.1f}%")
        
        # 触发告警回调
        for alert in alerts:
            for callback in self.alert_callbacks:
                try:
                    callback(alert, current_metrics)
                except Exception as e:
                    logger.error(f"告警回调执行失败: {str(e)}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        current_metrics = self.collector.get_current_metrics()
        metrics_history = self.collector.get_metrics_history(60)
        table_metrics = self.collector.get_table_metrics()
        connection_metrics = self.collector.get_connection_metrics()
        
        return {
            'current': current_metrics.__dict__ if current_metrics else {},
            'history': [m.__dict__ for m in metrics_history],
            'tables': {name: m.__dict__ for name, m in table_metrics.items()},
            'connections': {name: m.__dict__ for name, m in connection_metrics.items()},
            'analysis': self.analyzer.analyze_performance(60)
        }


# 全局性能监控实例
performance_monitor = PerformanceMonitor()
