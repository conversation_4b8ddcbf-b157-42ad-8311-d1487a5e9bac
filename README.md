# RDS Migration Tool

A comprehensive database migration tool that supports migrating between traditional databases, cloud RDS instances, and different RDS providers.

## Features

- **Multi-Database Support**: MySQL, PostgreSQL, SQL Server, Oracle
- **Flexible Migration Scenarios**:
  - Traditional Database → Cloud RDS
  - RDS → RDS (same or different providers)
  - RDS → Traditional Database
- **Schema and Data Migration**: Complete table structure, data, indexes, and constraints
- **Parallel Processing**: Multi-threaded migration for improved performance
- **Progress Monitoring**: Real-time progress tracking with detailed logging
- **Configuration Management**: YAML-based configuration with validation
- **Interactive CLI**: User-friendly command-line interface
- **Dry Run Support**: Test migrations without actual data transfer

## Installation

### Prerequisites

- Python 3.8 or higher
- Database drivers for your specific database types

### Install from Source

```bash
git clone <repository-url>
cd rds-migrate
pip install -r requirements.txt
pip install -e .
```

### Install Database Drivers

Depending on your database types, you may need additional drivers:

```bash
# For MySQL
pip install pymysql

# For PostgreSQL  
pip install psycopg2-binary

# For SQL Server
pip install pyodbc

# For Oracle
pip install cx_Oracle
```

## Quick Start

### 1. Initialize Configuration

Create a sample configuration file:

```bash
python main.py init --output my_migration.yaml
```

### 2. Edit Configuration

Edit the generated configuration file with your database details:

```yaml
source:
  host: "source-db.example.com"
  port: 3306
  database: "source_db"
  username: "source_user"
  password: "source_password"
  db_type: "mysql"

target:
  host: "target-rds.amazonaws.com"
  port: 3306
  database: "target_db"
  username: "target_user"
  password: "target_password"
  db_type: "mysql"

migration:
  tables: ["users", "orders", "products"]  # Optional: specific tables
  exclude_tables: ["temp_table"]           # Optional: tables to exclude
  batch_size: 1000
  parallel_workers: 4
  create_indexes: true
  create_foreign_keys: true
```

### 3. Test Connections

Verify database connectivity:

```bash
python main.py test -c my_migration.yaml
```

### 4. Analyze Source Database

Get detailed information about your source database:

```bash
python main.py analyze -c my_migration.yaml
```

### 5. Run Migration

Execute the migration:

```bash
python main.py migrate -c my_migration.yaml --log-level INFO
```

## Command Reference

### Initialize Configuration

```bash
python main.py init [--output CONFIG_FILE]
```

Creates a sample configuration file.

### Validate Configuration

```bash
python main.py validate -c CONFIG_FILE
```

Validates the configuration file format and parameters.

### Test Connections

```bash
python main.py test -c CONFIG_FILE
```

Tests connectivity to both source and target databases.

### Analyze Database

```bash
python main.py analyze -c CONFIG_FILE [--table TABLE_NAME]
```

Analyzes the source database structure and provides statistics.

### Run Migration

```bash
python main.py migrate -c CONFIG_FILE [OPTIONS]
```

Options:
- `--log-level`: Set logging level (DEBUG, INFO, WARNING, ERROR)
- `--log-file`: Save logs to file
- `--dry-run`: Perform a dry run without actual migration
- `--report`: Save migration report to file

### Interactive Configuration

```bash
python main.py interactive
```

Launches an interactive wizard to create configuration files.

## Configuration Options

### Database Configuration

```yaml
source/target:
  host: "database-host"
  port: 3306
  database: "database-name"
  username: "username"
  password: "password"
  db_type: "mysql"  # mysql, postgresql, sqlserver, oracle
  ssl_mode: "require"  # Optional: for PostgreSQL
  charset: "utf8mb4"   # Optional: for MySQL
```

### Migration Options

```yaml
migration:
  tables: ["table1", "table2"]      # Specific tables (optional)
  exclude_tables: ["temp_table"]    # Tables to exclude (optional)
  batch_size: 1000                  # Rows per batch
  parallel_workers: 4               # Number of parallel threads
  create_indexes: true              # Create indexes
  create_foreign_keys: true         # Create foreign keys
  data_only: false                  # Migrate data only (no schema)
  schema_only: false                # Migrate schema only (no data)
```

## Migration Scenarios

### Traditional MySQL to AWS RDS MySQL

```yaml
source:
  host: "on-premise-mysql.company.com"
  port: 3306
  database: "production_db"
  username: "mysql_user"
  password: "mysql_password"
  db_type: "mysql"

target:
  host: "myapp-db.cluster-xyz.us-east-1.rds.amazonaws.com"
  port: 3306
  database: "production_db"
  username: "admin"
  password: "rds_password"
  db_type: "mysql"
```

### PostgreSQL to PostgreSQL (Different Providers)

```yaml
source:
  host: "source-postgres.herokuapp.com"
  port: 5432
  database: "app_db"
  username: "postgres_user"
  password: "source_password"
  db_type: "postgresql"
  ssl_mode: "require"

target:
  host: "target-postgres.azure.com"
  port: 5432
  database: "app_db"
  username: "postgres_admin"
  password: "target_password"
  db_type: "postgresql"
  ssl_mode: "require"
```

### Cross-Database Migration (MySQL to PostgreSQL)

```yaml
source:
  host: "mysql-server.company.com"
  port: 3306
  database: "legacy_db"
  username: "mysql_user"
  password: "mysql_password"
  db_type: "mysql"

target:
  host: "postgres-rds.amazonaws.com"
  port: 5432
  database: "modern_db"
  username: "postgres_user"
  password: "postgres_password"
  db_type: "postgresql"
```

## Best Practices

1. **Test First**: Always run with `--dry-run` first
2. **Backup**: Create backups of both source and target databases
3. **Network**: Ensure stable network connection between databases
4. **Permissions**: Verify database user permissions for both read and write operations
5. **Resources**: Monitor CPU and memory usage during large migrations
6. **Batch Size**: Adjust batch size based on available memory and network speed
7. **Parallel Workers**: Start with fewer workers and increase based on performance

## Troubleshooting

### Connection Issues

- Verify database credentials and network connectivity
- Check firewall rules and security groups
- Ensure SSL/TLS settings match database requirements

### Performance Issues

- Reduce batch size for memory-constrained environments
- Adjust parallel workers based on database server capacity
- Consider migrating during off-peak hours

### Data Type Compatibility

- Review data type mappings between different database engines
- Test with a subset of data first
- Consider custom data transformation if needed

## Logging

The tool provides comprehensive logging:

- Console output with colored formatting
- Optional file logging with detailed information
- Progress bars for real-time status updates
- Migration reports with statistics and timing

## Security

- Store sensitive credentials in environment variables
- Use SSL/TLS connections when available
- Limit database user permissions to minimum required
- Consider using database connection pooling for large migrations

## Support

For issues, questions, or contributions, please refer to the project repository or contact the development team.
