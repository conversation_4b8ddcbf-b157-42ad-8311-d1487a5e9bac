# Kubernetes ConfigMap配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: rds-migrate-config
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
data:
  # 应用配置
  app.yaml: |
    app:
      name: "RDS Migration Tool"
      version: "2.0.0"
      environment: "production"
      debug: false
      
    server:
      host: "0.0.0.0"
      port: 8000
      workers: 4
      
    logging:
      level: "INFO"
      format: "json"
      file: "/app/logs/app.log"
      max_size: "100MB"
      backup_count: 10
      
    performance:
      max_connections: 100
      connection_timeout: 30
      query_timeout: 300
      batch_size: 1000
      
    security:
      encryption_enabled: true
      session_timeout: 3600
      max_login_attempts: 3
      audit_enabled: true

  # Nginx配置
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        
        gzip on;
        gzip_vary on;
        gzip_min_length 10240;
        gzip_proxied expired no-cache no-store private must-revalidate auth;
        gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
        
        upstream rds_migrate_backend {
            server rds-migrate-service:8000;
            keepalive 32;
        }
        
        server {
            listen 80;
            server_name _;
            
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            location / {
                proxy_pass http://rds_migrate_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
        }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rds-migrate-scripts
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
data:
  # 初始化脚本
  init.sh: |
    #!/bin/bash
    set -e
    
    echo "初始化RDS迁移工具..."
    
    # 创建必要目录
    mkdir -p /app/logs /app/data /app/config /app/backups
    
    # 设置权限
    chmod 755 /app/logs /app/data /app/config /app/backups
    
    # 等待数据库就绪
    echo "等待PostgreSQL就绪..."
    until pg_isready -h postgres-service -p 5432 -U postgres; do
      echo "PostgreSQL未就绪，等待中..."
      sleep 2
    done
    
    echo "等待Redis就绪..."
    until redis-cli -h redis-service -p 6379 ping; do
      echo "Redis未就绪，等待中..."
      sleep 2
    done
    
    echo "初始化完成！"
  
  # 健康检查脚本
  health-check.sh: |
    #!/bin/bash
    
    # 检查应用进程
    if ! pgrep -f "python.*rds_migrate" > /dev/null; then
        echo "应用进程未运行"
        exit 1
    fi
    
    # 检查HTTP端点
    if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "HTTP健康检查失败"
        exit 1
    fi
    
    echo "健康检查通过"
    exit 0
  
  # 备份脚本
  backup.sh: |
    #!/bin/bash
    set -e
    
    BACKUP_DIR="/app/backups"
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    
    echo "开始备份配置和数据..."
    
    # 备份配置
    tar -czf "${BACKUP_DIR}/config_${TIMESTAMP}.tar.gz" -C /app config/
    
    # 备份数据
    tar -czf "${BACKUP_DIR}/data_${TIMESTAMP}.tar.gz" -C /app data/
    
    # 清理旧备份（保留最近7天）
    find "${BACKUP_DIR}" -name "*.tar.gz" -mtime +7 -delete
    
    echo "备份完成: ${TIMESTAMP}"
