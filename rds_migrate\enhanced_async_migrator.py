"""
增强版异步迁移引擎 - 支持错误处理、重试机制、断点续传和故障恢复
优化版本包含增强的类型安全、内存管理和性能监控
"""

import asyncio
import logging
import time
import json
import hashlib
import gc
import sys
from typing import List, Dict, Any, Optional, Callable, Union, Set, Protocol, TypeVar, Generic
from dataclasses import dataclass, field
from enum import Enum, auto
from pathlib import Path
from contextlib import asynccontextmanager
import pickle
import weakref
from collections import defaultdict, deque

from .config import MigrationConfig, DatabaseConfig
from .connection_pool import pool_manager, PoolConfig
from .async_migrator import AsyncMigrationStats

logger = logging.getLogger(__name__)

# 类型变量
T = TypeVar('T')


class MigrationState(Enum):
    """迁移状态枚举，增强版本"""
    PENDING = "pending"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    RESUMING = "resuming"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    ROLLBACK = "rollback"
    CLEANUP = "cleanup"


class RetryStrategy(Enum):
    """重试策略枚举，增强版本"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    FIXED_DELAY = "fixed_delay"
    LINEAR_BACKOFF = "linear_backoff"
    ADAPTIVE = "adaptive"  # 自适应重试
    CIRCUIT_BREAKER = "circuit_breaker"  # 断路器模式


class CheckpointType(Enum):
    """检查点类型"""
    TABLE_START = auto()
    TABLE_COMPLETE = auto()
    BATCH_COMPLETE = auto()
    ERROR_RECOVERY = auto()
    MANUAL = auto()


@dataclass
class RetryConfig:
    """
    重试配置，增强版本包含更多策略选项
    """
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    backoff_multiplier: float = 2.0
    jitter: bool = True  # 添加随机抖动
    circuit_breaker_threshold: int = 5  # 断路器阈值
    circuit_breaker_timeout: float = 300.0  # 断路器超时（5分钟）

    def __post_init__(self):
        """验证配置参数"""
        if self.max_attempts < 1:
            raise ValueError("max_attempts must be >= 1")
        if self.base_delay < 0:
            raise ValueError("base_delay must be >= 0")
        if self.max_delay < self.base_delay:
            raise ValueError("max_delay must be >= base_delay")
        if self.backoff_multiplier <= 0:
            raise ValueError("backoff_multiplier must be > 0")


@dataclass
class CheckpointData:
    """
    检查点数据，用于断点续传
    """
    migration_id: str
    timestamp: float
    state: MigrationState
    checkpoint_type: CheckpointType
    table_name: Optional[str] = None
    batch_offset: int = 0
    rows_processed: int = 0
    total_rows: int = 0
    completed_tables: Set[str] = field(default_factory=set)
    failed_tables: Set[str] = field(default_factory=set)
    error_log: List[Dict[str, Any]] = field(default_factory=list)
    performance_stats: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式用于序列化"""
        return {
            'migration_id': self.migration_id,
            'timestamp': self.timestamp,
            'state': self.state.value,
            'checkpoint_type': self.checkpoint_type.name,
            'table_name': self.table_name,
            'batch_offset': self.batch_offset,
            'rows_processed': self.rows_processed,
            'total_rows': self.total_rows,
            'completed_tables': list(self.completed_tables),
            'failed_tables': list(self.failed_tables),
            'error_log': self.error_log,
            'performance_stats': self.performance_stats
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CheckpointData':
        """从字典创建检查点数据"""
        return cls(
            migration_id=data['migration_id'],
            timestamp=data['timestamp'],
            state=MigrationState(data['state']),
            checkpoint_type=CheckpointType[data['checkpoint_type']],
            table_name=data.get('table_name'),
            batch_offset=data.get('batch_offset', 0),
            rows_processed=data.get('rows_processed', 0),
            total_rows=data.get('total_rows', 0),
            completed_tables=set(data.get('completed_tables', [])),
            failed_tables=set(data.get('failed_tables', [])),
            error_log=data.get('error_log', []),
            performance_stats=data.get('performance_stats', {})
        )


@dataclass
class TableMigrationState:
    """表迁移状态"""
    table_name: str
    total_rows: int = 0
    migrated_rows: int = 0
    failed_batches: Set[int] = field(default_factory=set)
    completed_batches: Set[int] = field(default_factory=set)
    state: MigrationState = MigrationState.PENDING
    error_count: int = 0
    last_error: Optional[str] = None
    checksum: Optional[str] = None


@dataclass
class MigrationCheckpoint:
    """迁移检查点"""
    migration_id: str
    timestamp: float
    table_states: Dict[str, TableMigrationState]
    global_state: MigrationState
    config_hash: str
    
    def save_to_file(self, filepath: Path):
        """保存检查点到文件"""
        with open(filepath, 'wb') as f:
            pickle.dump(self, f)
    
    @classmethod
    def load_from_file(cls, filepath: Path) -> 'MigrationCheckpoint':
        """从文件加载检查点"""
        with open(filepath, 'rb') as f:
            return pickle.load(f)


class EnhancedAsyncMigrationEngine:
    """增强版异步迁移引擎"""
    
    def __init__(self, config: MigrationConfig, retry_config: RetryConfig = None):
        self.config = config
        self.retry_config = retry_config or RetryConfig()
        self.migration_id = self._generate_migration_id()
        self.checkpoint_dir = Path("checkpoints")
        self.checkpoint_dir.mkdir(exist_ok=True)
        
        # 状态管理
        self.table_states: Dict[str, TableMigrationState] = {}
        self.global_state = MigrationState.PENDING
        self.stats = AsyncMigrationStats()
        
        # 连接池配置
        self.pool_config = PoolConfig(
            min_size=2,
            max_size=config.parallel_workers * 2,
            health_check_interval=30,
            connection_timeout=60
        )
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
        
        # 控制标志
        self._should_stop = False
        self._is_paused = False
        
    def _generate_migration_id(self) -> str:
        """生成迁移ID"""
        config_str = f"{self.config.source.host}:{self.config.target.host}:{time.time()}"
        return hashlib.md5(config_str.encode()).hexdigest()[:12]
    
    def _get_config_hash(self) -> str:
        """获取配置哈希"""
        config_dict = {
            'source': {
                'host': self.config.source.host,
                'port': self.config.source.port,
                'database': self.config.source.database,
                'db_type': self.config.source.db_type
            },
            'target': {
                'host': self.config.target.host,
                'port': self.config.target.port,
                'database': self.config.target.database,
                'db_type': self.config.target.db_type
            },
            'migration': {
                'batch_size': self.config.batch_size,
                'parallel_workers': self.config.parallel_workers,
                'tables': self.config.tables,
                'exclude_tables': self.config.exclude_tables
            }
        }
        config_str = json.dumps(config_dict, sort_keys=True)
        return hashlib.sha256(config_str.encode()).hexdigest()
    
    async def _retry_with_backoff(self, func: Callable, *args, **kwargs) -> Any:
        """带退避的重试机制"""
        last_exception = None
        
        for attempt in range(self.retry_config.max_attempts):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt == self.retry_config.max_attempts - 1:
                    break
                
                # 计算延迟时间
                if self.retry_config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
                    delay = min(
                        self.retry_config.base_delay * (self.retry_config.backoff_multiplier ** attempt),
                        self.retry_config.max_delay
                    )
                elif self.retry_config.strategy == RetryStrategy.LINEAR_BACKOFF:
                    delay = min(
                        self.retry_config.base_delay * (attempt + 1),
                        self.retry_config.max_delay
                    )
                else:  # FIXED_DELAY
                    delay = self.retry_config.base_delay
                
                logger.warning(f"操作失败，{delay}秒后重试 (尝试 {attempt + 1}/{self.retry_config.max_attempts}): {str(e)}")
                await asyncio.sleep(delay)
        
        raise last_exception
    
    async def _save_checkpoint(self):
        """保存检查点"""
        checkpoint = MigrationCheckpoint(
            migration_id=self.migration_id,
            timestamp=time.time(),
            table_states=self.table_states,
            global_state=self.global_state,
            config_hash=self._get_config_hash()
        )
        
        checkpoint_file = self.checkpoint_dir / f"{self.migration_id}.checkpoint"
        checkpoint.save_to_file(checkpoint_file)
        logger.debug(f"检查点已保存: {checkpoint_file}")
    
    async def load_checkpoint(self, checkpoint_file: Path = None) -> bool:
        """加载检查点"""
        if checkpoint_file is None:
            checkpoint_file = self.checkpoint_dir / f"{self.migration_id}.checkpoint"
        
        if not checkpoint_file.exists():
            logger.info("未找到检查点文件，将开始新的迁移")
            return False
        
        try:
            checkpoint = MigrationCheckpoint.load_from_file(checkpoint_file)
            
            # 验证配置是否匹配
            if checkpoint.config_hash != self._get_config_hash():
                logger.warning("配置已更改，无法使用现有检查点")
                return False
            
            # 恢复状态
            self.table_states = checkpoint.table_states
            self.global_state = checkpoint.global_state
            
            logger.info(f"已加载检查点: {checkpoint_file}")
            return True
            
        except Exception as e:
            logger.error(f"加载检查点失败: {str(e)}")
            return False
    
    async def _migrate_table_with_recovery(self, table_name: str) -> Dict[str, Any]:
        """带恢复功能的表迁移"""
        if table_name not in self.table_states:
            self.table_states[table_name] = TableMigrationState(table_name=table_name)
        
        table_state = self.table_states[table_name]
        
        try:
            table_state.state = MigrationState.RUNNING
            
            # 获取连接池
            source_pool = await pool_manager.get_async_pool(self.config.source, self.pool_config)
            target_pool = await pool_manager.get_async_pool(self.config.target, self.pool_config)
            
            # 获取表总行数（如果还未获取）
            if table_state.total_rows == 0:
                async with source_pool.get_connection() as conn:
                    result = await conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                    table_state.total_rows = result[0][0] if result else 0
            
            # 计算批次
            batch_size = self.config.batch_size
            total_batches = (table_state.total_rows + batch_size - 1) // batch_size
            
            # 创建批次任务
            semaphore = asyncio.Semaphore(self.config.parallel_workers)
            tasks = []
            
            for batch_num in range(total_batches):
                # 跳过已完成的批次
                if batch_num in table_state.completed_batches:
                    continue
                
                offset = batch_num * batch_size
                task = self._migrate_batch_with_retry(
                    semaphore, source_pool, target_pool, 
                    table_name, offset, batch_size, batch_num
                )
                tasks.append(task)
            
            # 执行批次迁移
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理结果
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        table_state.error_count += 1
                        table_state.last_error = str(result)
                        logger.error(f"批次迁移失败: {str(result)}")
                    else:
                        table_state.migrated_rows += result.get('rows_migrated', 0)
            
            # 验证数据一致性
            await self._verify_table_data(table_name, source_pool, target_pool)
            
            table_state.state = MigrationState.COMPLETED
            logger.info(f"表 {table_name} 迁移完成")
            
            return {
                'table_name': table_name,
                'total_rows': table_state.total_rows,
                'migrated_rows': table_state.migrated_rows,
                'error_count': table_state.error_count
            }
            
        except Exception as e:
            table_state.state = MigrationState.FAILED
            table_state.last_error = str(e)
            logger.error(f"表 {table_name} 迁移失败: {str(e)}")
            
            if self.error_callback:
                await self.error_callback(table_name, e)
            
            raise
        
        finally:
            # 保存检查点
            await self._save_checkpoint()
    
    async def _migrate_batch_with_retry(self, semaphore, source_pool, target_pool, 
                                      table_name: str, offset: int, batch_size: int, batch_num: int):
        """带重试的批次迁移"""
        async with semaphore:
            return await self._retry_with_backoff(
                self._migrate_single_batch,
                source_pool, target_pool, table_name, offset, batch_size, batch_num
            )
    
    async def _migrate_single_batch(self, source_pool, target_pool, 
                                  table_name: str, offset: int, batch_size: int, batch_num: int):
        """迁移单个批次"""
        start_time = time.time()
        
        try:
            # 从源数据库读取数据
            async with source_pool.get_connection() as source_conn:
                query = f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
                rows = await source_conn.execute(query)
            
            if not rows:
                return {'rows_migrated': 0, 'batch_time': time.time() - start_time}
            
            # 写入目标数据库
            async with target_pool.get_connection() as target_conn:
                # 构建插入语句
                if rows:
                    columns = list(rows[0].keys()) if hasattr(rows[0], 'keys') else range(len(rows[0]))
                    placeholders = ', '.join(['%s'] * len(columns))
                    insert_query = f"INSERT INTO {table_name} VALUES ({placeholders})"
                    
                    # 批量插入
                    values = [tuple(row.values()) if hasattr(row, 'values') else tuple(row) for row in rows]
                    await target_conn.execute(insert_query, values)
            
            # 标记批次完成
            table_state = self.table_states[table_name]
            table_state.completed_batches.add(batch_num)
            
            batch_time = time.time() - start_time
            
            # 更新进度
            if self.progress_callback:
                await self.progress_callback(table_name, table_state.migrated_rows + len(rows), table_state.total_rows)
            
            return {'rows_migrated': len(rows), 'batch_time': batch_time}
            
        except Exception as e:
            # 标记批次失败
            table_state = self.table_states[table_name]
            table_state.failed_batches.add(batch_num)
            logger.error(f"批次 {batch_num} 迁移失败: {str(e)}")
            raise
    
    async def _verify_table_data(self, table_name: str, source_pool, target_pool):
        """验证表数据一致性"""
        try:
            # 比较行数
            async with source_pool.get_connection() as source_conn:
                source_result = await source_conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                source_count = source_result[0][0] if source_result else 0
            
            async with target_pool.get_connection() as target_conn:
                target_result = await target_conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                target_count = target_result[0][0] if target_result else 0
            
            if source_count != target_count:
                raise Exception(f"数据行数不匹配: 源表 {source_count} 行, 目标表 {target_count} 行")
            
            logger.info(f"表 {table_name} 数据验证通过: {source_count} 行")
            
        except Exception as e:
            logger.error(f"表 {table_name} 数据验证失败: {str(e)}")
            raise
    
    async def migrate_async(self) -> AsyncMigrationStats:
        """执行异步迁移"""
        try:
            self.global_state = MigrationState.RUNNING
            self.stats.start_time = time.time()
            
            logger.info(f"开始异步迁移 (ID: {self.migration_id})")
            
            # 尝试加载检查点
            await self.load_checkpoint()
            
            # 获取要迁移的表列表
            tables = await self._get_tables_to_migrate()
            
            # 并发迁移表
            semaphore = asyncio.Semaphore(min(len(tables), self.config.parallel_workers))
            tasks = []
            
            for table_name in tables:
                if self._should_stop:
                    break
                
                task = self._migrate_table_with_recovery(table_name)
                tasks.append(task)
            
            # 等待所有表迁移完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    self.stats.errors += 1
                    logger.error(f"表迁移失败: {str(result)}")
                else:
                    self.stats.tables_migrated += 1
                    self.stats.rows_migrated += result.get('migrated_rows', 0)
            
            self.global_state = MigrationState.COMPLETED
            self.stats.end_time = time.time()
            
            logger.info(f"异步迁移完成: {self.stats.tables_migrated} 表, {self.stats.rows_migrated} 行")
            
            return self.stats
            
        except Exception as e:
            self.global_state = MigrationState.FAILED
            self.stats.end_time = time.time()
            logger.error(f"异步迁移失败: {str(e)}")
            raise
        
        finally:
            # 清理检查点文件（如果迁移成功）
            if self.global_state == MigrationState.COMPLETED:
                checkpoint_file = self.checkpoint_dir / f"{self.migration_id}.checkpoint"
                if checkpoint_file.exists():
                    checkpoint_file.unlink()
    
    async def _get_tables_to_migrate(self) -> List[str]:
        """获取要迁移的表列表"""
        # 这里应该从数据库获取表列表，简化实现
        if self.config.tables:
            return self.config.tables
        else:
            # 从源数据库获取所有表
            source_pool = await pool_manager.get_async_pool(self.config.source, self.pool_config)
            async with source_pool.get_connection() as conn:
                if self.config.source.db_type == 'mysql':
                    result = await conn.execute("SHOW TABLES")
                    tables = [row[0] for row in result]
                elif self.config.source.db_type == 'postgresql':
                    result = await conn.execute(
                        "SELECT tablename FROM pg_tables WHERE schemaname = 'public'"
                    )
                    tables = [row[0] for row in result]
                else:
                    raise ValueError(f"不支持的数据库类型: {self.config.source.db_type}")
            
            # 排除指定的表
            if self.config.exclude_tables:
                tables = [t for t in tables if t not in self.config.exclude_tables]
            
            return tables
    
    def pause(self):
        """暂停迁移"""
        self._is_paused = True
        self.global_state = MigrationState.PAUSED
        logger.info("迁移已暂停")
    
    def resume(self):
        """恢复迁移"""
        self._is_paused = False
        self.global_state = MigrationState.RUNNING
        logger.info("迁移已恢复")
    
    def stop(self):
        """停止迁移"""
        self._should_stop = True
        self.global_state = MigrationState.CANCELLED
        logger.info("迁移已停止")
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调"""
        self.progress_callback = callback
    
    def set_error_callback(self, callback: Callable):
        """设置错误回调"""
        self.error_callback = callback
    
    def get_migration_status(self) -> Dict[str, Any]:
        """获取迁移状态"""
        return {
            'migration_id': self.migration_id,
            'global_state': self.global_state.value,
            'table_states': {
                name: {
                    'state': state.state.value,
                    'total_rows': state.total_rows,
                    'migrated_rows': state.migrated_rows,
                    'error_count': state.error_count,
                    'progress': (state.migrated_rows / state.total_rows * 100) if state.total_rows > 0 else 0
                }
                for name, state in self.table_states.items()
            },
            'stats': {
                'tables_migrated': self.stats.tables_migrated,
                'rows_migrated': self.stats.rows_migrated,
                'errors': self.stats.errors,
                'start_time': self.stats.start_time,
                'end_time': self.stats.end_time
            }
        }
