<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RDS迁移工具 - 企业级仪表板</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="navbar-content">
                <a href="{{ url_for('dashboard') }}" class="navbar-brand">
                    🚀 RDS Migration Pro
                </a>
                <ul class="navbar-nav">
                    <li><a href="{{ url_for('dashboard') }}" class="active">仪表板</a></li>
                    <li><a href="{{ url_for('migration') }}">迁移管理</a></li>
                    <li><a href="{{ url_for('monitoring') }}">实时监控</a></li>
                    <li><a href="{{ url_for('validation') }}">数据验证</a></li>
                    <li><a href="{{ url_for('security') }}">安全中心</a></li>
                    <li><a href="{{ url_for('settings') }}">系统设置</a></li>
                </ul>
                <button class="theme-toggle" data-tooltip="切换主题">
                    🌙
                </button>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="main-content">
            <!-- 快速状态概览 -->
            <div class="dashboard-grid">
                <div class="metric-card">
                    <div class="metric-value" data-metric="active_migrations">0</div>
                    <div class="metric-label">活跃迁移</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" data-metric="completed_migrations">0</div>
                    <div class="metric-label">已完成迁移</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" data-metric="total_tables">0</div>
                    <div class="metric-label">总表数量</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" data-metric="data_transferred">0</div>
                    <div class="metric-label">数据传输量(GB)</div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="dashboard-grid">
                <!-- 性能监控图表 -->
                <div class="chart-container">
                    <h3 class="chart-title">系统性能监控</h3>
                    <div style="height: 300px;">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>

                <!-- 迁移进度图表 -->
                <div class="chart-container">
                    <h3 class="chart-title">迁移进度概览</h3>
                    <div style="height: 300px;">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 错误统计图表 -->
            <div class="chart-container">
                <h3 class="chart-title">错误统计分析</h3>
                <div style="height: 250px;">
                    <canvas id="errorChart"></canvas>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">最近活动</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>操作</th>
                                    <th>状态</th>
                                    <th>详情</th>
                                </tr>
                            </thead>
                            <tbody id="recent-activities">
                                <tr>
                                    <td>2024-01-15 14:30:25</td>
                                    <td>开始迁移 users 表</td>
                                    <td><span class="badge badge-info">进行中</span></td>
                                    <td>MySQL → PostgreSQL</td>
                                </tr>
                                <tr>
                                    <td>2024-01-15 14:25:10</td>
                                    <td>完成迁移 products 表</td>
                                    <td><span class="badge badge-success">成功</span></td>
                                    <td>10,000 行数据</td>
                                </tr>
                                <tr>
                                    <td>2024-01-15 14:20:05</td>
                                    <td>数据验证完成</td>
                                    <td><span class="badge badge-success">通过</span></td>
                                    <td>100% 一致性</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">快速操作</h3>
                </div>
                <div class="card-body">
                    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        <button class="btn btn-primary modal-trigger" data-modal="new-migration-modal">
                            ➕ 新建迁移
                        </button>
                        <button class="btn btn-secondary modal-trigger" data-modal="validation-modal">
                            ✅ 数据验证
                        </button>
                        <button class="btn btn-outline">
                            📊 生成报告
                        </button>
                        <button class="btn btn-outline">
                            ⚙️ 系统设置
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新建迁移模态框 -->
    <div id="new-migration-modal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">创建新的数据迁移</h3>
                <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <form class="ajax-form" action="/api/migrations" method="POST">
                    <div class="form-group">
                        <label class="form-label">迁移名称</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">源数据库类型</label>
                        <select name="source_type" class="form-control" required>
                            <option value="">请选择</option>
                            <option value="mysql">MySQL</option>
                            <option value="postgresql">PostgreSQL</option>
                            <option value="oracle">Oracle</option>
                            <option value="sqlserver">SQL Server</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">目标数据库类型</label>
                        <select name="target_type" class="form-control" required>
                            <option value="">请选择</option>
                            <option value="mysql">MySQL</option>
                            <option value="postgresql">PostgreSQL</option>
                            <option value="oracle">Oracle</option>
                            <option value="sqlserver">SQL Server</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">迁移模式</label>
                        <select name="mode" class="form-control" required>
                            <option value="full">完整迁移</option>
                            <option value="incremental">增量迁移</option>
                            <option value="schema_only">仅结构</option>
                            <option value="data_only">仅数据</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-close">取消</button>
                <button type="submit" form="new-migration-form" class="btn btn-primary">创建迁移</button>
            </div>
        </div>
    </div>

    <!-- 数据验证模态框 -->
    <div id="validation-modal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">数据一致性验证</h3>
                <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <form class="ajax-form" action="/api/validation" method="POST">
                    <div class="form-group">
                        <label class="form-label">验证级别</label>
                        <select name="level" class="form-control" required>
                            <option value="basic">基础验证（行数、结构）</option>
                            <option value="standard">标准验证（+ 数据类型）</option>
                            <option value="comprehensive">全面验证（+ 内容校验）</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">验证表（留空验证所有表）</label>
                        <input type="text" name="tables" class="form-control" placeholder="table1,table2,table3">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-close">取消</button>
                <button type="submit" form="validation-form" class="btn btn-primary">开始验证</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/enhanced-ui.js') }}"></script>
    <script>
        // 页面特定的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化仪表板数据
            loadDashboardData();
            
            // 设置定时刷新
            setInterval(loadDashboardData, 30000); // 每30秒刷新一次
        });

        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                const data = await response.json();
                
                // 更新指标卡片
                updateMetricCards(data.metrics);
                
                // 更新活动列表
                updateRecentActivities(data.activities);
                
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
            }
        }

        function updateMetricCards(metrics) {
            Object.entries(metrics).forEach(([key, value]) => {
                const element = document.querySelector(`[data-metric="${key}"]`);
                if (element) {
                    // 使用动画更新数值
                    animateNumber(element, value);
                }
            });
        }

        function updateRecentActivities(activities) {
            const tbody = document.getElementById('recent-activities');
            if (tbody && activities) {
                tbody.innerHTML = activities.map(activity => `
                    <tr>
                        <td>${activity.timestamp}</td>
                        <td>${activity.action}</td>
                        <td><span class="badge badge-${activity.status_type}">${activity.status}</span></td>
                        <td>${activity.details}</td>
                    </tr>
                `).join('');
            }
        }

        function animateNumber(element, targetValue) {
            const startValue = parseInt(element.textContent) || 0;
            const duration = 1000;
            const startTime = performance.now();
            
            const animate = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
                element.textContent = currentValue.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            };
            
            requestAnimationFrame(animate);
        }
    </script>
</body>
</html>
