#!/usr/bin/env python3
"""
Simple test script for RDS Migration Tool - Basic structure validation
This script tests basic functionality without requiring external dependencies.
"""

import sys
import os
from pathlib import Path

def test_file_structure():
    """Test that all required files exist."""
    print("🧪 Testing File Structure...")
    
    required_files = [
        "main.py",
        "requirements.txt", 
        "setup.py",
        "README.md",
        "USAGE_GUIDE.md",
        "rds_migrate/__init__.py",
        "rds_migrate/config.py",
        "rds_migrate/database.py",
        "rds_migrate/migrator.py",
        "rds_migrate/logger.py",
        "rds_migrate/cli.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All core files present")
    return True


def test_python_imports():
    """Test that Python modules can be imported."""
    print("\n🧪 Testing Python Module Imports...")
    
    # Add current directory to path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # Test basic imports without external dependencies
        import rds_migrate
        print("✅ rds_migrate package imported successfully")
        
        # Test individual modules (may fail due to missing dependencies, but structure should be OK)
        module_files = [
            "rds_migrate.config",
            "rds_migrate.database", 
            "rds_migrate.migrator",
            "rds_migrate.logger",
            "rds_migrate.cli"
        ]
        
        importable = []
        failed = []
        
        for module in module_files:
            try:
                __import__(module)
                importable.append(module)
            except ImportError as e:
                # Expected due to missing dependencies
                failed.append(f"{module}: {str(e)}")
        
        print(f"✅ Module structure verified")
        if failed:
            print(f"ℹ️  Some modules failed to import (expected due to missing dependencies):")
            for failure in failed[:3]:  # Show first 3 failures
                print(f"   - {failure}")
        
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False
    
    return True


def test_example_files():
    """Test that example files exist."""
    print("\n🧪 Testing Example Files...")
    
    example_dir = Path("examples")
    if not example_dir.exists():
        print("❌ Examples directory missing")
        return False
    
    example_files = [
        "mysql_to_aws_rds.yaml",
        "postgres_to_postgres.yaml", 
        "mysql_to_postgresql.yaml",
        "schema_only_migration.yaml",
        "data_only_migration.yaml"
    ]
    
    missing = []
    for file_name in example_files:
        if not (example_dir / file_name).exists():
            missing.append(file_name)
    
    if missing:
        print(f"❌ Missing example files: {missing}")
        return False
    
    print("✅ All example files present")
    return True


def test_main_entry_point():
    """Test that main.py can be executed."""
    print("\n🧪 Testing Main Entry Point...")
    
    try:
        # Test that main.py exists and has proper structure
        with open("main.py", "r") as f:
            content = f.read()
            
        if "from rds_migrate.cli import cli" in content:
            print("✅ Main entry point structure verified")
            return True
        else:
            print("❌ Main entry point structure invalid")
            return False
            
    except Exception as e:
        print(f"❌ Main entry point test failed: {str(e)}")
        return False


def test_documentation():
    """Test that documentation files exist and have content."""
    print("\n🧪 Testing Documentation...")
    
    doc_files = ["README.md", "USAGE_GUIDE.md"]
    
    for doc_file in doc_files:
        try:
            with open(doc_file, "r", encoding="utf-8") as f:
                content = f.read()
                
            if len(content) < 100:  # Basic content check
                print(f"❌ {doc_file} appears to be empty or too short")
                return False
                
            print(f"✅ {doc_file} has content")
            
        except Exception as e:
            print(f"❌ Documentation test failed for {doc_file}: {str(e)}")
            return False
    
    return True


def main():
    """Run all basic tests."""
    print("🚀 RDS Migration Tool - Basic Structure Tests")
    print("=" * 60)
    
    tests = [
        test_file_structure,
        test_python_imports,
        test_example_files,
        test_main_entry_point,
        test_documentation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 Basic structure tests passed! The RDS Migration Tool structure is correct.")
        print("\n📝 Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run full tests: python test_tool.py")
        print("3. Create configuration: python main.py init")
        print("4. Edit configuration with your database details")
        print("5. Test connections: python main.py test -c your_config.yaml")
        print("6. Run migration: python main.py migrate -c your_config.yaml")
    else:
        print("❌ Some basic tests failed. Please check the errors above.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
