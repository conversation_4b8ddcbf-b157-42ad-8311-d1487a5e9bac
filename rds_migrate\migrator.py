"""
Core migration engine for RDS migration tool.
"""

import logging
import time
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from dataclasses import dataclass
from tqdm import tqdm
from sqlalchemy import text, MetaData, Table, create_engine
from sqlalchemy.schema import CreateTable, CreateIndex
from .config import MigrationConfig
from .database import DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class MigrationStats:
    """Migration statistics."""
    tables_migrated: int = 0
    rows_migrated: int = 0
    errors: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    
    @property
    def duration(self) -> float:
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0


class MigrationEngine:
    """Core migration engine."""
    
    def __init__(self, config: MigrationConfig):
        self.config = config
        self.source_db = DatabaseManager(config.source)
        self.target_db = DatabaseManager(config.target)
        self.stats = MigrationStats()
        self.progress_callback: Optional[Callable] = None
    
    def set_progress_callback(self, callback: Callable):
        """Set progress callback function."""
        self.progress_callback = callback
    
    def connect_databases(self):
        """Connect to source and target databases."""
        logger.info("Connecting to source database...")
        self.source_db.connect()
        
        logger.info("Connecting to target database...")
        self.target_db.connect()
        
        logger.info("Database connections established")
    
    def disconnect_databases(self):
        """Disconnect from databases."""
        self.source_db.disconnect()
        self.target_db.disconnect()
        logger.info("Database connections closed")
    
    def get_tables_to_migrate(self) -> List[str]:
        """Get list of tables to migrate."""
        all_tables = self.source_db.get_table_list()
        
        if self.config.tables:
            # Only migrate specified tables
            tables = [t for t in self.config.tables if t in all_tables]
            missing = set(self.config.tables) - set(tables)
            if missing:
                logger.warning(f"Tables not found in source: {missing}")
        else:
            # Migrate all tables
            tables = all_tables
        
        if self.config.exclude_tables:
            # Exclude specified tables
            tables = [t for t in tables if t not in self.config.exclude_tables]
        
        logger.info(f"Tables to migrate: {len(tables)}")
        return tables
    
    def migrate_table_schema(self, table_name: str) -> bool:
        """Migrate table schema."""
        try:
            logger.info(f"Migrating schema for table: {table_name}")
            
            # Get source table schema
            with self.source_db.get_connection() as source_conn:
                metadata = MetaData()
                source_table = Table(table_name, metadata, autoload_with=source_conn)
            
            # Create table in target database
            with self.target_db.get_connection() as target_conn:
                # Check if table already exists
                target_metadata = MetaData()
                target_metadata.reflect(bind=target_conn)
                
                if table_name in target_metadata.tables:
                    logger.warning(f"Table {table_name} already exists in target database")
                    return True
                
                # Create table
                create_stmt = CreateTable(source_table)
                target_conn.execute(create_stmt)
                target_conn.commit()
                
                logger.info(f"Schema migrated for table: {table_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to migrate schema for {table_name}: {str(e)}")
            self.stats.errors += 1
            return False
    
    def migrate_table_data(self, table_name: str) -> bool:
        """Migrate table data."""
        try:
            logger.info(f"Migrating data for table: {table_name}")
            
            # Get row count
            total_rows = self.source_db.get_row_count(table_name)
            if total_rows == 0:
                logger.info(f"Table {table_name} is empty, skipping data migration")
                return True
            
            # Get table schema for column names
            schema = self.source_db.get_table_schema(table_name)
            columns = [col['name'] for col in schema['columns']]
            column_list = ', '.join(columns)
            placeholders = ', '.join([':' + col for col in columns])
            
            # Migrate data in batches
            batch_size = self.config.batch_size
            offset = 0
            migrated_rows = 0
            
            with tqdm(total=total_rows, desc=f"Migrating {table_name}") as pbar:
                while offset < total_rows:
                    # Fetch batch from source
                    query = f"SELECT {column_list} FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
                    
                    with self.source_db.get_connection() as source_conn:
                        result = source_conn.execute(text(query))
                        batch_data = result.fetchall()
                    
                    if not batch_data:
                        break
                    
                    # Insert batch into target
                    insert_query = f"INSERT INTO {table_name} ({column_list}) VALUES ({placeholders})"
                    
                    with self.target_db.get_connection() as target_conn:
                        for row in batch_data:
                            row_dict = dict(zip(columns, row))
                            target_conn.execute(text(insert_query), row_dict)
                        target_conn.commit()
                    
                    migrated_rows += len(batch_data)
                    offset += batch_size
                    pbar.update(len(batch_data))
                    
                    if self.progress_callback:
                        self.progress_callback(table_name, migrated_rows, total_rows)
            
            self.stats.rows_migrated += migrated_rows
            logger.info(f"Data migrated for table {table_name}: {migrated_rows} rows")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate data for {table_name}: {str(e)}")
            self.stats.errors += 1
            return False
    
    def migrate_table_indexes(self, table_name: str) -> bool:
        """Migrate table indexes."""
        if not self.config.create_indexes:
            return True
            
        try:
            logger.info(f"Migrating indexes for table: {table_name}")
            
            with self.source_db.get_connection() as source_conn:
                metadata = MetaData()
                source_table = Table(table_name, metadata, autoload_with=source_conn)
                
                with self.target_db.get_connection() as target_conn:
                    for index in source_table.indexes:
                        if not index.unique:  # Skip unique indexes (usually handled by constraints)
                            create_index = CreateIndex(index)
                            try:
                                target_conn.execute(create_index)
                                target_conn.commit()
                            except Exception as e:
                                logger.warning(f"Failed to create index {index.name}: {str(e)}")
            
            logger.info(f"Indexes migrated for table: {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate indexes for {table_name}: {str(e)}")
            return False
    
    def migrate_table(self, table_name: str) -> bool:
        """Migrate a single table (schema + data + indexes)."""
        success = True
        
        if not self.config.data_only:
            success &= self.migrate_table_schema(table_name)
        
        if not self.config.schema_only and success:
            success &= self.migrate_table_data(table_name)
        
        if not self.config.data_only and success:
            success &= self.migrate_table_indexes(table_name)
        
        if success:
            self.stats.tables_migrated += 1
        
        return success
    
    def migrate_parallel(self, tables: List[str]) -> bool:
        """Migrate tables in parallel."""
        logger.info(f"Starting parallel migration with {self.config.parallel_workers} workers")
        
        success = True
        with ThreadPoolExecutor(max_workers=self.config.parallel_workers) as executor:
            future_to_table = {executor.submit(self.migrate_table, table): table 
                             for table in tables}
            
            for future in as_completed(future_to_table):
                table = future_to_table[future]
                try:
                    result = future.result()
                    if not result:
                        success = False
                        logger.error(f"Failed to migrate table: {table}")
                except Exception as e:
                    success = False
                    logger.error(f"Exception during migration of {table}: {str(e)}")
        
        return success
    
    def migrate(self) -> MigrationStats:
        """Execute the complete migration process."""
        self.stats.start_time = time.time()
        
        try:
            logger.info("Starting database migration...")
            
            # Connect to databases
            self.connect_databases()
            
            # Get tables to migrate
            tables = self.get_tables_to_migrate()
            
            if not tables:
                logger.warning("No tables to migrate")
                return self.stats
            
            # Migrate tables
            if self.config.parallel_workers > 1:
                success = self.migrate_parallel(tables)
            else:
                success = True
                for table in tables:
                    if not self.migrate_table(table):
                        success = False
            
            if success:
                logger.info("Migration completed successfully")
            else:
                logger.error("Migration completed with errors")
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            self.stats.errors += 1
        
        finally:
            self.disconnect_databases()
            self.stats.end_time = time.time()
        
        return self.stats
