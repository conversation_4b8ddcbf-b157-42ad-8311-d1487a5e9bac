"""
Core migration engine for RDS migration tool.
Optimized version with enhanced type safety, error handling, and performance.
"""

import logging
import time
from typing import List, Dict, Any, Optional, Callable, Union, Tuple, Set
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from contextlib import contextmanager
from tqdm import tqdm
from sqlalchemy import text, MetaData, Table, create_engine, Engine
from sqlalchemy.schema import CreateTable, CreateIndex
from sqlalchemy.exc import SQLAlchemyError, DatabaseError, OperationalError
from .config import MigrationConfig
from .database import DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class MigrationStats:
    """Migration statistics with enhanced tracking."""
    tables_migrated: int = 0
    rows_migrated: int = 0
    errors: int = 0
    warnings: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    failed_tables: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)

    @property
    def duration(self) -> float:
        """Calculate migration duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        total_operations = self.tables_migrated + len(self.failed_tables)
        if total_operations == 0:
            return 0.0
        return (self.tables_migrated / total_operations) * 100.0

    @property
    def rows_per_second(self) -> float:
        """Calculate average rows migrated per second."""
        if self.duration > 0:
            return self.rows_migrated / self.duration
        return 0.0


class MigrationEngine:
    """
    Core migration engine with enhanced error handling and performance optimization.

    Features:
    - Type-safe operations
    - Enhanced error handling with specific exception types
    - Performance monitoring and optimization
    - Memory-efficient batch processing
    - Connection pooling and management
    """

    def __init__(self, config: MigrationConfig) -> None:
        self.config = config
        self.source_db = DatabaseManager(config.source)
        self.target_db = DatabaseManager(config.target)
        self.stats = MigrationStats()
        self.progress_callback: Optional[Callable[[str, int, int], None]] = None
        self._connection_cache: Dict[str, Engine] = {}
        self._metadata_cache: Dict[str, MetaData] = {}

    def set_progress_callback(self, callback: Callable[[str, int, int], None]) -> None:
        """
        Set progress callback function.

        Args:
            callback: Function that receives (operation, current, total) parameters
        """
        self.progress_callback = callback
    
    @contextmanager
    def database_connections(self):
        """
        Context manager for database connections with automatic cleanup.

        Yields:
            Tuple of (source_db, target_db) connections
        """
        try:
            logger.info("Establishing database connections...")
            self.connect_databases()
            yield self.source_db, self.target_db
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            self.disconnect_databases()

    def connect_databases(self) -> None:
        """
        Connect to source and target databases with enhanced error handling.

        Raises:
            DatabaseError: If connection fails
        """
        try:
            logger.info("Connecting to source database...")
            self.source_db.connect()

            logger.info("Connecting to target database...")
            self.target_db.connect()

            # Verify connections
            self._verify_connections()
            logger.info("Database connections established successfully")

        except Exception as e:
            logger.error(f"Failed to establish database connections: {e}")
            # Cleanup partial connections
            try:
                self.disconnect_databases()
            except Exception:
                pass
            raise DatabaseError(f"Connection failed: {e}") from e

    def disconnect_databases(self) -> None:
        """Disconnect from databases with error handling."""
        errors = []

        try:
            if hasattr(self.source_db, 'disconnect'):
                self.source_db.disconnect()
        except Exception as e:
            errors.append(f"Source disconnect error: {e}")

        try:
            if hasattr(self.target_db, 'disconnect'):
                self.target_db.disconnect()
        except Exception as e:
            errors.append(f"Target disconnect error: {e}")

        # Clear connection cache
        self._connection_cache.clear()
        self._metadata_cache.clear()

        if errors:
            logger.warning(f"Disconnect errors: {'; '.join(errors)}")
        else:
            logger.info("Database connections closed successfully")

    def _verify_connections(self) -> None:
        """Verify database connections are working."""
        try:
            # Test source connection
            with self.source_db.get_connection() as conn:
                conn.execute(text("SELECT 1"))

            # Test target connection
            with self.target_db.get_connection() as conn:
                conn.execute(text("SELECT 1"))

        except Exception as e:
            raise DatabaseError(f"Connection verification failed: {e}") from e

    def get_tables_to_migrate(self) -> List[str]:
        """
        Get list of tables to migrate with validation.

        Returns:
            List of table names to migrate

        Raises:
            ValueError: If no tables found to migrate
        """
        try:
            all_tables = self.source_db.get_table_list()
            if not all_tables:
                raise ValueError("No tables found in source database")

            # Filter tables based on configuration
            tables = self._filter_tables(all_tables)

            if not tables:
                raise ValueError("No tables match the migration criteria")

            logger.info(f"Tables to migrate: {len(tables)} out of {len(all_tables)} total")
            return tables

        except Exception as e:
            logger.error(f"Failed to get tables list: {e}")
            raise

    def _filter_tables(self, all_tables: List[str]) -> List[str]:
        """Filter tables based on include/exclude configuration."""
        tables = all_tables.copy()

        # Apply include filter
        if self.config.tables:
            included_tables = [t for t in self.config.tables if t in all_tables]
            missing_tables = set(self.config.tables) - set(included_tables)

            if missing_tables:
                logger.warning(f"Specified tables not found in source: {missing_tables}")
                self.stats.warnings += len(missing_tables)

            tables = included_tables

        # Apply exclude filter
        if self.config.exclude_tables:
            excluded_count = len([t for t in tables if t in self.config.exclude_tables])
            tables = [t for t in tables if t not in self.config.exclude_tables]

            if excluded_count > 0:
                logger.info(f"Excluded {excluded_count} tables from migration")

        return tables
    
    def migrate_table_schema(self, table_name: str) -> bool:
        """
        Migrate table schema with enhanced error handling and caching.

        Args:
            table_name: Name of the table to migrate schema for

        Returns:
            True if schema migration successful, False otherwise
        """
        start_time = time.time()

        try:
            logger.info(f"Migrating schema for table: {table_name}")

            # Check cache first
            cache_key = f"schema_{table_name}"
            if cache_key in self._metadata_cache:
                source_table = self._metadata_cache[cache_key]
            else:
                # Get source table schema
                source_table = self._get_source_table_metadata(table_name)
                self._metadata_cache[cache_key] = source_table

            # Create table in target database
            success = self._create_target_table(table_name, source_table)

            if success:
                duration = time.time() - start_time
                self.stats.performance_metrics[f"schema_{table_name}"] = duration
                logger.info(f"Schema migrated for table: {table_name} ({duration:.2f}s)")

            return success

        except SQLAlchemyError as e:
            logger.error(f"Database error migrating schema for {table_name}: {e}")
            self.stats.errors += 1
            self.stats.failed_tables.append(table_name)
            return False
        except Exception as e:
            logger.error(f"Unexpected error migrating schema for {table_name}: {e}")
            self.stats.errors += 1
            self.stats.failed_tables.append(table_name)
            return False

    def _get_source_table_metadata(self, table_name: str) -> Table:
        """Get source table metadata with error handling."""
        try:
            with self.source_db.get_connection() as source_conn:
                metadata = MetaData()
                source_table = Table(table_name, metadata, autoload_with=source_conn)
                return source_table
        except Exception as e:
            raise DatabaseError(f"Failed to load source table metadata for {table_name}: {e}") from e

    def _create_target_table(self, table_name: str, source_table: Table) -> bool:
        """Create table in target database."""
        try:
            with self.target_db.get_connection() as target_conn:
                # Check if table already exists
                if self._table_exists_in_target(table_name, target_conn):
                    logger.warning(f"Table {table_name} already exists in target database")
                    self.stats.warnings += 1
                    return True

                # Create table
                create_stmt = CreateTable(source_table)
                target_conn.execute(create_stmt)
                target_conn.commit()

                return True

        except Exception as e:
            raise DatabaseError(f"Failed to create target table {table_name}: {e}") from e

    def _table_exists_in_target(self, table_name: str, connection) -> bool:
        """Check if table exists in target database."""
        try:
            target_metadata = MetaData()
            target_metadata.reflect(bind=connection)
            return table_name in target_metadata.tables
        except Exception as e:
            logger.warning(f"Could not check if table {table_name} exists: {e}")
            return False
    
    def migrate_table_data(self, table_name: str) -> bool:
        """
        Migrate table data with optimized batch processing and memory management.

        Args:
            table_name: Name of the table to migrate data for

        Returns:
            True if data migration successful, False otherwise
        """
        start_time = time.time()

        try:
            logger.info(f"Migrating data for table: {table_name}")

            # Get row count and validate
            total_rows = self._get_table_row_count(table_name)
            if total_rows == 0:
                logger.info(f"Table {table_name} is empty, skipping data migration")
                return True

            # Get optimized column information
            columns_info = self._get_table_columns_optimized(table_name)
            if not columns_info:
                logger.error(f"Could not get column information for table {table_name}")
                return False

            # Perform batch migration with memory optimization
            migrated_rows = self._migrate_data_in_batches(table_name, columns_info, total_rows)

            # Update statistics
            self.stats.rows_migrated += migrated_rows
            duration = time.time() - start_time
            self.stats.performance_metrics[f"data_{table_name}"] = duration

            logger.info(f"Data migrated for table {table_name}: {migrated_rows}/{total_rows} rows ({duration:.2f}s)")
            return migrated_rows == total_rows

        except OperationalError as e:
            logger.error(f"Database operational error migrating data for {table_name}: {e}")
            self.stats.errors += 1
            self.stats.failed_tables.append(table_name)
            return False
        except Exception as e:
            logger.error(f"Unexpected error migrating data for {table_name}: {e}")
            self.stats.errors += 1
            self.stats.failed_tables.append(table_name)
            return False

    def _get_table_row_count(self, table_name: str) -> int:
        """Get table row count with error handling."""
        try:
            return self.source_db.get_row_count(table_name)
        except Exception as e:
            logger.warning(f"Could not get exact row count for {table_name}, using estimate: {e}")
            # Fallback to approximate count
            try:
                with self.source_db.get_connection() as conn:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    return result.scalar() or 0
            except Exception:
                return 0

    def _get_table_columns_optimized(self, table_name: str) -> Optional[Dict[str, Any]]:
        """Get table column information with caching and optimization."""
        cache_key = f"columns_{table_name}"

        if cache_key in self._metadata_cache:
            return self._metadata_cache[cache_key]

        try:
            schema = self.source_db.get_table_schema(table_name)
            columns = [col['name'] for col in schema['columns']]

            columns_info = {
                'names': columns,
                'list': ', '.join(columns),
                'placeholders': ', '.join([f':{col}' for col in columns]),
                'count': len(columns)
            }

            self._metadata_cache[cache_key] = columns_info
            return columns_info

        except Exception as e:
            logger.error(f"Failed to get column information for {table_name}: {e}")
            return None

    def _migrate_data_in_batches(self, table_name: str, columns_info: Dict[str, Any], total_rows: int) -> int:
        """Migrate data in optimized batches with progress tracking."""
        batch_size = min(self.config.batch_size, 10000)  # Cap batch size for memory
        offset = 0
        migrated_rows = 0

        # Prepare queries
        select_query = f"SELECT {columns_info['list']} FROM {table_name} LIMIT {batch_size} OFFSET {{offset}}"
        insert_query = f"INSERT INTO {table_name} ({columns_info['list']}) VALUES ({columns_info['placeholders']})"

        try:
            with tqdm(total=total_rows, desc=f"Migrating {table_name}", unit="rows") as pbar:
                while offset < total_rows:
                    batch_rows = self._process_data_batch(
                        select_query.format(offset=offset),
                        insert_query,
                        columns_info['names'],
                        batch_size
                    )

                    if batch_rows == 0:
                        break

                    migrated_rows += batch_rows
                    offset += batch_size
                    pbar.update(batch_rows)

                    # Progress callback
                    if self.progress_callback:
                        self.progress_callback(f"Migrating {table_name}", migrated_rows, total_rows)

            return migrated_rows

        except Exception as e:
            logger.error(f"Batch migration failed for {table_name}: {e}")
            raise

    def _process_data_batch(self, select_query: str, insert_query: str, columns: List[str], batch_size: int) -> int:
        """Process a single data batch with optimized memory usage."""
        try:
            # Fetch batch from source
            with self.source_db.get_connection() as source_conn:
                result = source_conn.execute(text(select_query))
                batch_data = result.fetchmany(batch_size)

            if not batch_data:
                return 0

            # Prepare batch for insertion
            batch_dicts = [dict(zip(columns, row)) for row in batch_data]

            # Insert batch into target using executemany for better performance
            with self.target_db.get_connection() as target_conn:
                target_conn.execute(text(insert_query), batch_dicts)
                target_conn.commit()

            return len(batch_data)

        except Exception as e:
            logger.error(f"Batch processing failed: {e}")
            raise
    
    def migrate_table_indexes(self, table_name: str) -> bool:
        """Migrate table indexes."""
        if not self.config.create_indexes:
            return True
            
        try:
            logger.info(f"Migrating indexes for table: {table_name}")
            
            with self.source_db.get_connection() as source_conn:
                metadata = MetaData()
                source_table = Table(table_name, metadata, autoload_with=source_conn)
                
                with self.target_db.get_connection() as target_conn:
                    for index in source_table.indexes:
                        if not index.unique:  # Skip unique indexes (usually handled by constraints)
                            create_index = CreateIndex(index)
                            try:
                                target_conn.execute(create_index)
                                target_conn.commit()
                            except Exception as e:
                                logger.warning(f"Failed to create index {index.name}: {str(e)}")
            
            logger.info(f"Indexes migrated for table: {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate indexes for {table_name}: {str(e)}")
            return False
    
    def migrate_table(self, table_name: str) -> bool:
        """Migrate a single table (schema + data + indexes)."""
        success = True
        
        if not self.config.data_only:
            success &= self.migrate_table_schema(table_name)
        
        if not self.config.schema_only and success:
            success &= self.migrate_table_data(table_name)
        
        if not self.config.data_only and success:
            success &= self.migrate_table_indexes(table_name)
        
        if success:
            self.stats.tables_migrated += 1
        
        return success
    
    def migrate_parallel(self, tables: List[str]) -> bool:
        """Migrate tables in parallel."""
        logger.info(f"Starting parallel migration with {self.config.parallel_workers} workers")
        
        success = True
        with ThreadPoolExecutor(max_workers=self.config.parallel_workers) as executor:
            future_to_table = {executor.submit(self.migrate_table, table): table 
                             for table in tables}
            
            for future in as_completed(future_to_table):
                table = future_to_table[future]
                try:
                    result = future.result()
                    if not result:
                        success = False
                        logger.error(f"Failed to migrate table: {table}")
                except Exception as e:
                    success = False
                    logger.error(f"Exception during migration of {table}: {str(e)}")
        
        return success
    
    def migrate(self) -> MigrationStats:
        """Execute the complete migration process."""
        self.stats.start_time = time.time()
        
        try:
            logger.info("Starting database migration...")
            
            # Connect to databases
            self.connect_databases()
            
            # Get tables to migrate
            tables = self.get_tables_to_migrate()
            
            if not tables:
                logger.warning("No tables to migrate")
                return self.stats
            
            # Migrate tables
            if self.config.parallel_workers > 1:
                success = self.migrate_parallel(tables)
            else:
                success = True
                for table in tables:
                    if not self.migrate_table(table):
                        success = False
            
            if success:
                logger.info("Migration completed successfully")
            else:
                logger.error("Migration completed with errors")
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            self.stats.errors += 1
        
        finally:
            self.disconnect_databases()
            self.stats.end_time = time.time()
        
        return self.stats
