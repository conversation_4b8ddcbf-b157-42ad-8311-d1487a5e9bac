<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RDS迁移工具 - 实时监控</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="navbar-content">
                <a href="{{ url_for('dashboard') }}" class="navbar-brand">
                    🚀 RDS Migration Pro
                </a>
                <ul class="navbar-nav">
                    <li><a href="{{ url_for('dashboard') }}">仪表板</a></li>
                    <li><a href="{{ url_for('migration') }}">迁移管理</a></li>
                    <li><a href="{{ url_for('monitoring') }}" class="active">实时监控</a></li>
                    <li><a href="{{ url_for('validation') }}">数据验证</a></li>
                    <li><a href="{{ url_for('security') }}">安全中心</a></li>
                    <li><a href="{{ url_for('settings') }}">系统设置</a></li>
                </ul>
                <button class="theme-toggle" data-tooltip="切换主题">
                    🌙
                </button>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="main-content">
            <!-- 实时状态指标 -->
            <div class="dashboard-grid">
                <div class="metric-card">
                    <div class="metric-value" data-metric="cpu">0%</div>
                    <div class="metric-label">CPU使用率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" data-metric="memory">0%</div>
                    <div class="metric-label">内存使用率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" data-metric="network">0 MB/s</div>
                    <div class="metric-label">网络吞吐量</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" data-metric="connections">0</div>
                    <div class="metric-label">活跃连接数</div>
                </div>
            </div>

            <!-- 实时性能图表 -->
            <div class="dashboard-grid">
                <div class="chart-container">
                    <h3 class="chart-title">系统资源使用率</h3>
                    <div style="height: 300px;">
                        <canvas id="systemResourceChart"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <h3 class="chart-title">数据库连接池状态</h3>
                    <div style="height: 300px;">
                        <canvas id="connectionPoolChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 迁移任务实时状态 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">迁移任务实时状态</h3>
                    <div style="display: flex; gap: 1rem;">
                        <button class="btn btn-sm btn-outline" onclick="pauseAllMigrations()">
                            ⏸️ 暂停所有
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="resumeAllMigrations()">
                            ▶️ 恢复所有
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="refreshMigrationStatus()">
                            🔄 刷新状态
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>源数据库</th>
                                    <th>目标数据库</th>
                                    <th>当前表</th>
                                    <th>进度</th>
                                    <th>速度</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="migration-tasks">
                                <!-- 动态加载的迁移任务 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 表级别详细监控 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">表级别迁移监控</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>表名</th>
                                    <th>总行数</th>
                                    <th>已迁移</th>
                                    <th>进度</th>
                                    <th>速度(行/秒)</th>
                                    <th>预计完成时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="table-monitoring">
                                <!-- 动态加载的表监控数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 错误和警告日志 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">实时日志监控</h3>
                    <div style="display: flex; gap: 1rem;">
                        <select id="log-level-filter" class="form-control" style="width: auto;">
                            <option value="all">所有级别</option>
                            <option value="error">错误</option>
                            <option value="warning">警告</option>
                            <option value="info">信息</option>
                        </select>
                        <button class="btn btn-sm btn-outline" onclick="clearLogs()">
                            🗑️ 清空日志
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="exportLogs()">
                            📥 导出日志
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="log-container" style="height: 300px; overflow-y: auto; background: var(--bg-tertiary); padding: 1rem; border-radius: var(--border-radius); font-family: monospace; font-size: 0.875rem;">
                        <!-- 实时日志内容 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/enhanced-ui.js') }}"></script>
    <script>
        let systemResourceChart, connectionPoolChart;
        let logContainer;

        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            initLogMonitoring();
            startRealTimeUpdates();
        });

        function initCharts() {
            // 系统资源图表
            const systemCtx = document.getElementById('systemResourceChart');
            systemResourceChart = new Chart(systemCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率',
                        data: [],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }, {
                        label: '内存使用率',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }, {
                        label: '磁盘I/O',
                        data: [],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // 连接池状态图表
            const poolCtx = document.getElementById('connectionPoolChart');
            connectionPoolChart = new Chart(poolCtx, {
                type: 'doughnut',
                data: {
                    labels: ['活跃连接', '空闲连接', '可用连接'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: ['#ef4444', '#f59e0b', '#10b981'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initLogMonitoring() {
            logContainer = document.getElementById('log-container');
            
            // 日志级别过滤器
            document.getElementById('log-level-filter').addEventListener('change', function() {
                filterLogs(this.value);
            });
        }

        function startRealTimeUpdates() {
            // 每秒更新系统指标
            setInterval(updateSystemMetrics, 1000);
            
            // 每5秒更新迁移状态
            setInterval(updateMigrationStatus, 5000);
            
            // 每2秒更新表监控
            setInterval(updateTableMonitoring, 2000);
        }

        async function updateSystemMetrics() {
            try {
                const response = await fetch('/api/monitoring/system');
                const data = await response.json();
                
                // 更新指标卡片
                document.querySelector('[data-metric="cpu"]').textContent = data.cpu + '%';
                document.querySelector('[data-metric="memory"]').textContent = data.memory + '%';
                document.querySelector('[data-metric="network"]').textContent = data.network + ' MB/s';
                document.querySelector('[data-metric="connections"]').textContent = data.connections;
                
                // 更新图表
                updateSystemResourceChart(data);
                updateConnectionPoolChart(data.connectionPool);
                
            } catch (error) {
                console.error('更新系统指标失败:', error);
            }
        }

        function updateSystemResourceChart(data) {
            const chart = systemResourceChart;
            const now = new Date().toLocaleTimeString();
            
            chart.data.labels.push(now);
            chart.data.datasets[0].data.push(data.cpu);
            chart.data.datasets[1].data.push(data.memory);
            chart.data.datasets[2].data.push(data.diskIO);
            
            // 保持最近60个数据点
            if (chart.data.labels.length > 60) {
                chart.data.labels.shift();
                chart.data.datasets.forEach(dataset => dataset.data.shift());
            }
            
            chart.update('none');
        }

        function updateConnectionPoolChart(poolData) {
            const chart = connectionPoolChart;
            chart.data.datasets[0].data = [
                poolData.active,
                poolData.idle,
                poolData.available
            ];
            chart.update('none');
        }

        async function updateMigrationStatus() {
            try {
                const response = await fetch('/api/monitoring/migrations');
                const data = await response.json();
                
                const tbody = document.getElementById('migration-tasks');
                tbody.innerHTML = data.tasks.map(task => `
                    <tr data-task-id="${task.id}">
                        <td>${task.name}</td>
                        <td>${task.source}</td>
                        <td>${task.target}</td>
                        <td>${task.currentTable}</td>
                        <td>
                            <div class="progress">
                                <div class="progress-bar" style="width: ${task.progress}%"></div>
                            </div>
                            <small>${task.progress}%</small>
                        </td>
                        <td>${task.speed}</td>
                        <td><span class="badge badge-${task.statusType}">${task.status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline" onclick="pauseTask('${task.id}')">⏸️</button>
                            <button class="btn btn-sm btn-outline" onclick="stopTask('${task.id}')">⏹️</button>
                        </td>
                    </tr>
                `).join('');
                
            } catch (error) {
                console.error('更新迁移状态失败:', error);
            }
        }

        async function updateTableMonitoring() {
            try {
                const response = await fetch('/api/monitoring/tables');
                const data = await response.json();
                
                const tbody = document.getElementById('table-monitoring');
                tbody.innerHTML = data.tables.map(table => `
                    <tr data-table="${table.name}">
                        <td>${table.name}</td>
                        <td>${table.totalRows.toLocaleString()}</td>
                        <td>${table.migratedRows.toLocaleString()}</td>
                        <td>
                            <div class="progress">
                                <div class="progress-bar" style="width: ${table.progress}%"></div>
                            </div>
                            <small>${table.progress}%</small>
                        </td>
                        <td>${table.speed}</td>
                        <td>${table.eta}</td>
                        <td><span class="badge badge-${table.statusType}">${table.status}</span></td>
                    </tr>
                `).join('');
                
            } catch (error) {
                console.error('更新表监控失败:', error);
            }
        }

        function addLogEntry(level, message, timestamp) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${level}`;
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level">[${level.toUpperCase()}]</span>
                <span class="log-message">${message}</span>
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条目数量
            while (logContainer.children.length > 1000) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        function filterLogs(level) {
            const entries = logContainer.querySelectorAll('.log-entry');
            entries.forEach(entry => {
                if (level === 'all' || entry.classList.contains(`log-${level}`)) {
                    entry.style.display = 'block';
                } else {
                    entry.style.display = 'none';
                }
            });
        }

        function clearLogs() {
            logContainer.innerHTML = '';
        }

        function exportLogs() {
            const logs = Array.from(logContainer.children).map(entry => entry.textContent).join('\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `migration-logs-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 任务控制函数
        async function pauseTask(taskId) {
            try {
                await fetch(`/api/migrations/${taskId}/pause`, { method: 'POST' });
                window.enhancedUI.showNotification('任务已暂停', 'info');
            } catch (error) {
                window.enhancedUI.showNotification('暂停任务失败', 'error');
            }
        }

        async function stopTask(taskId) {
            if (confirm('确定要停止这个迁移任务吗？')) {
                try {
                    await fetch(`/api/migrations/${taskId}/stop`, { method: 'POST' });
                    window.enhancedUI.showNotification('任务已停止', 'warning');
                } catch (error) {
                    window.enhancedUI.showNotification('停止任务失败', 'error');
                }
            }
        }

        async function pauseAllMigrations() {
            try {
                await fetch('/api/migrations/pause-all', { method: 'POST' });
                window.enhancedUI.showNotification('所有任务已暂停', 'info');
            } catch (error) {
                window.enhancedUI.showNotification('暂停所有任务失败', 'error');
            }
        }

        async function resumeAllMigrations() {
            try {
                await fetch('/api/migrations/resume-all', { method: 'POST' });
                window.enhancedUI.showNotification('所有任务已恢复', 'success');
            } catch (error) {
                window.enhancedUI.showNotification('恢复所有任务失败', 'error');
            }
        }

        function refreshMigrationStatus() {
            updateMigrationStatus();
            updateTableMonitoring();
            window.enhancedUI.showNotification('状态已刷新', 'info');
        }

        // WebSocket消息处理
        if (window.enhancedUI && window.enhancedUI.websocket) {
            const originalHandler = window.enhancedUI.handleWebSocketMessage;
            window.enhancedUI.handleWebSocketMessage = function(data) {
                originalHandler.call(this, data);
                
                // 处理日志消息
                if (data.type === 'log_entry') {
                    addLogEntry(data.payload.level, data.payload.message, data.payload.timestamp);
                }
            };
        }
    </script>
</body>
</html>
