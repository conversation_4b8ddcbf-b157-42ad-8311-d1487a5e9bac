#!/usr/bin/env python3
"""
RDS迁移工具启动脚本
自动检测依赖并选择最佳的启动方式
"""

import sys
import os
import subprocess

def check_dependencies():
    """检查依赖包是否安装"""
    missing_deps = []
    
    try:
        import click
    except ImportError:
        missing_deps.append('click')
    
    try:
        import yaml
    except ImportError:
        missing_deps.append('pyyaml')
    
    try:
        import tabulate
    except ImportError:
        missing_deps.append('tabulate')
    
    try:
        import sqlalchemy
    except ImportError:
        missing_deps.append('sqlalchemy')
    
    try:
        import pymysql
    except ImportError:
        missing_deps.append('pymysql')
    
    try:
        import psycopg2
    except ImportError:
        missing_deps.append('psycopg2-binary')
    
    try:
        import tqdm
    except ImportError:
        missing_deps.append('tqdm')
    
    return missing_deps

def install_dependencies(missing_deps):
    """使用国内镜像安装缺失的依赖"""
    if not missing_deps:
        return True
    
    print(f"🔧 检测到缺失的依赖包: {', '.join(missing_deps)}")
    print("📦 正在使用清华大学镜像安装依赖包...")
    
    try:
        cmd = [
            sys.executable, '-m', 'pip', 'install', 
            '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple/'
        ] + missing_deps
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功！")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def start_cli():
    """启动CLI版本"""
    try:
        from rds_migrate.cli import main
        print("🚀 启动CLI版本...")
        main()
    except Exception as e:
        print(f"❌ CLI启动失败: {e}")
        print("🔄 尝试使用简化版本...")
        start_simple()

def start_gui():
    """启动GUI版本"""
    try:
        from rds_migrate.gui.main_window import main
        print("🚀 启动GUI版本...")
        main()
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        print("🔄 尝试使用CLI版本...")
        start_cli()

def start_simple():
    """启动简化版本"""
    try:
        import simple_migrator
        print("🚀 启动简化版本...")
        simple_migrator.main()
    except Exception as e:
        print(f"❌ 简化版本启动失败: {e}")
        print("请检查Python环境配置")

def main():
    """主函数"""
    print("=" * 60)
    print("🗄️  RDS数据库迁移工具")
    print("=" * 60)
    
    # 检查依赖
    missing_deps = check_dependencies()
    
    if missing_deps:
        print("⚠️  检测到缺失的依赖包")
        response = input("是否自动安装缺失的依赖包？(y/N): ")
        
        if response.lower() in ['y', 'yes']:
            if not install_dependencies(missing_deps):
                print("🔄 依赖安装失败，使用简化版本...")
                start_simple()
                return
        else:
            print("🔄 使用简化版本...")
            start_simple()
            return
    
    # 选择启动方式
    print("\n请选择启动方式:")
    print("1. GUI图形界面 (推荐)")
    print("2. CLI命令行界面")
    print("3. 简化版本 (无外部依赖)")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                start_gui()
                break
            elif choice == '2':
                start_cli()
                break
            elif choice == '3':
                start_simple()
                break
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入1-4")
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == '__main__':
    main()
