"""
配置管理模块测试
"""

import pytest
import json
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open

from rds_migrate.config import ConfigManager, DatabaseConfig, MigrationConfig


@pytest.mark.unit
@pytest.mark.config
class TestDatabaseConfig:
    """数据库配置测试"""
    
    def test_database_config_creation(self):
        """测试数据库配置创建"""
        config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="test_db",
            username="user",
            password="pass"
        )
        
        assert config.db_type == "mysql"
        assert config.host == "localhost"
        assert config.port == 3306
        assert config.database == "test_db"
        assert config.username == "user"
        assert config.password == "pass"
    
    def test_database_config_to_dict(self):
        """测试数据库配置转换为字典"""
        config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="test_db",
            username="user",
            password="pass"
        )

        config_dict = config.to_dict()
        assert config_dict["db_type"] == "mysql"
        assert config_dict["host"] == "localhost"
        assert config_dict["port"] == 3306
        assert config_dict["database"] == "test_db"
        assert config_dict["username"] == "user"
        assert config_dict["password"] == "pass"

    def test_database_config_with_ssl(self):
        """测试带SSL的数据库配置"""
        config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="test_db",
            username="user",
            password="pass",
            ssl_mode="require"
        )

        assert config.ssl_mode == "require"
        config_dict = config.to_dict()
        assert config_dict["ssl_mode"] == "require"

    def test_database_config_charset(self):
        """测试数据库字符集配置"""
        config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="test_db",
            username="user",
            password="pass",
            charset="utf8"
        )

        assert config.charset == "utf8"
        config_dict = config.to_dict()
        assert config_dict["charset"] == "utf8"


@pytest.mark.unit
@pytest.mark.config
class TestConfigManager:
    """配置管理器测试"""
    
    def test_load_yaml_config(self, sample_yaml_config, sample_config_dict):
        """测试加载YAML配置"""
        manager = ConfigManager()
        config = manager.load_config(str(sample_yaml_config))
        
        assert isinstance(config, MigrationConfig)
        assert config.source.db_type == sample_config_dict["source"]["db_type"]
        assert config.target.db_type == sample_config_dict["target"]["db_type"]
        assert config.batch_size == sample_config_dict["batch_size"]
    
    def test_load_json_config(self, sample_json_config, sample_config_dict):
        """测试加载JSON配置"""
        manager = ConfigManager()
        config = manager.load_config(str(sample_json_config))
        
        assert isinstance(config, MigrationConfig)
        assert config.source.db_type == sample_config_dict["source"]["db_type"]
        assert config.target.db_type == sample_config_dict["target"]["db_type"]
        assert config.batch_size == sample_config_dict["batch_size"]
    
    def test_load_nonexistent_config(self):
        """测试加载不存在的配置文件"""
        manager = ConfigManager()
        
        with pytest.raises(FileNotFoundError):
            manager.load_config("nonexistent_config.yaml")
    
    def test_create_sample_config(self, temp_dir):
        """测试创建示例配置"""
        manager = ConfigManager()
        config_file = temp_dir / "sample.yaml"
        
        manager.create_sample_config(str(config_file))
        
        assert config_file.exists()
        
        # 验证生成的配置可以正常加载
        config = manager.load_config(str(config_file))
        assert isinstance(config, MigrationConfig)
    
    def test_save_config(self, temp_dir, sample_config_dict):
        """测试保存配置"""
        manager = ConfigManager()
        config_file = temp_dir / "test_config.yaml"

        # 创建配置对象
        source_config = DatabaseConfig(**sample_config_dict["source"])
        target_config = DatabaseConfig(**sample_config_dict["target"])
        migration_config = MigrationConfig(
            source=source_config,
            target=target_config,
            batch_size=sample_config_dict["batch_size"],
            parallel_workers=sample_config_dict["parallel_workers"]
        )

        # 保存配置
        manager.save_config(migration_config, str(config_file))

        # 验证文件存在
        assert config_file.exists()

        # 验证可以重新加载
        loaded_config = manager.load_config(str(config_file))
        assert loaded_config.batch_size == sample_config_dict["batch_size"]
    
    @patch("os.path.exists", return_value=True)
    @patch("builtins.open", new_callable=mock_open, read_data="invalid: yaml: content:")
    def test_load_invalid_yaml(self, mock_file, mock_exists):
        """测试加载无效YAML文件"""
        manager = ConfigManager()

        with pytest.raises(yaml.YAMLError):
            manager.load_config("invalid.yaml")

    @patch("os.path.exists", return_value=True)
    @patch("builtins.open", new_callable=mock_open, read_data='invalid: yaml: content:')
    def test_load_invalid_json(self, mock_file, mock_exists):
        """测试加载无效YAML文件"""
        manager = ConfigManager()

        with pytest.raises((yaml.YAMLError, KeyError)):
            manager.load_config("invalid.yaml")


@pytest.mark.unit
@pytest.mark.config
class TestMigrationConfig:
    """迁移配置测试"""

    def test_migration_config_creation(self, sample_config_dict):
        """测试迁移配置创建"""
        source_config = DatabaseConfig(**sample_config_dict["source"])
        target_config = DatabaseConfig(**sample_config_dict["target"])

        config = MigrationConfig(
            source=source_config,
            target=target_config,
            batch_size=sample_config_dict["batch_size"],
            parallel_workers=sample_config_dict["parallel_workers"],
            tables=sample_config_dict.get("tables")
        )

        assert isinstance(config.source, DatabaseConfig)
        assert isinstance(config.target, DatabaseConfig)
        assert config.batch_size == sample_config_dict["batch_size"]
        assert config.parallel_workers == sample_config_dict["parallel_workers"]
        assert config.tables == sample_config_dict.get("tables")

    def test_migration_config_defaults(self):
        """测试迁移配置默认值"""
        source_config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="source_db",
            username="user",
            password="pass"
        )

        target_config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="target_db",
            username="user",
            password="pass"
        )

        config = MigrationConfig(source=source_config, target=target_config)

        # 检查默认值
        assert config.batch_size == 1000
        assert config.parallel_workers == 4
        assert config.schema_only == False
        assert config.data_only == False
        assert config.create_indexes == True
        assert config.create_foreign_keys == True

    def test_migration_config_to_dict(self, sample_config_dict):
        """测试迁移配置转换为字典"""
        source_config = DatabaseConfig(**sample_config_dict["source"])
        target_config = DatabaseConfig(**sample_config_dict["target"])

        config = MigrationConfig(
            source=source_config,
            target=target_config,
            batch_size=sample_config_dict["batch_size"],
            parallel_workers=sample_config_dict["parallel_workers"]
        )

        config_dict = config.to_dict()
        assert config_dict["batch_size"] == sample_config_dict["batch_size"]
        assert config_dict["parallel_workers"] == sample_config_dict["parallel_workers"]
        assert "source" in config_dict
        assert "target" in config_dict
