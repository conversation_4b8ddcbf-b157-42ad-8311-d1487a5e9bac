"""
数据一致性验证器 - 迁移前后的数据一致性检查和验证
"""

import asyncio
import hashlib
import logging
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import json
import time

from .connection_pool import pool_manager, PoolConfig
from .config import DatabaseConfig

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """验证级别"""
    BASIC = "basic"          # 基础验证：行数、表结构
    STANDARD = "standard"    # 标准验证：基础 + 数据类型、约束
    COMPREHENSIVE = "comprehensive"  # 全面验证：标准 + 数据内容校验和


class ValidationStatus(Enum):
    """验证状态"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"


@dataclass
class ValidationResult:
    """验证结果"""
    table_name: str
    validation_type: str
    status: ValidationStatus
    message: str
    details: Dict[str, Any] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class TableValidationSummary:
    """表验证摘要"""
    table_name: str
    total_checks: int
    passed_checks: int
    failed_checks: int
    warning_checks: int
    overall_status: ValidationStatus
    validation_time: float
    results: List[ValidationResult]


class DataValidator:
    """数据验证器"""
    
    def __init__(self, source_config: DatabaseConfig, target_config: DatabaseConfig,
                 validation_level: ValidationLevel = ValidationLevel.STANDARD):
        self.source_config = source_config
        self.target_config = target_config
        self.validation_level = validation_level
        
        # 连接池配置
        self.pool_config = PoolConfig(min_size=1, max_size=5)
        
        # 验证结果
        self.validation_results: Dict[str, List[ValidationResult]] = {}
        self.table_summaries: Dict[str, TableValidationSummary] = {}
        
        # 进度回调
        self.progress_callback: Optional[callable] = None
    
    async def validate_migration(self, tables: List[str] = None) -> Dict[str, TableValidationSummary]:
        """验证迁移结果"""
        logger.info(f"开始数据一致性验证 (级别: {self.validation_level.value})")
        
        # 获取要验证的表列表
        if tables is None:
            tables = await self._get_common_tables()
        
        # 并发验证表
        semaphore = asyncio.Semaphore(3)  # 限制并发数
        tasks = []
        
        for table_name in tables:
            task = self._validate_table_with_semaphore(semaphore, table_name)
            tasks.append(task)
        
        # 等待所有验证完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"数据一致性验证完成，共验证 {len(tables)} 个表")
        return self.table_summaries
    
    async def _validate_table_with_semaphore(self, semaphore: asyncio.Semaphore, table_name: str):
        """带信号量的表验证"""
        async with semaphore:
            await self._validate_table(table_name)
    
    async def _validate_table(self, table_name: str):
        """验证单个表"""
        start_time = time.time()
        results = []
        
        try:
            logger.debug(f"开始验证表: {table_name}")
            
            # 基础验证
            results.extend(await self._validate_row_count(table_name))
            results.extend(await self._validate_table_structure(table_name))
            
            # 标准验证
            if self.validation_level in [ValidationLevel.STANDARD, ValidationLevel.COMPREHENSIVE]:
                results.extend(await self._validate_data_types(table_name))
                results.extend(await self._validate_constraints(table_name))
                results.extend(await self._validate_indexes(table_name))
            
            # 全面验证
            if self.validation_level == ValidationLevel.COMPREHENSIVE:
                results.extend(await self._validate_data_content(table_name))
            
            # 统计结果
            passed = len([r for r in results if r.status == ValidationStatus.PASSED])
            failed = len([r for r in results if r.status == ValidationStatus.FAILED])
            warnings = len([r for r in results if r.status == ValidationStatus.WARNING])
            
            # 确定整体状态
            if failed > 0:
                overall_status = ValidationStatus.FAILED
            elif warnings > 0:
                overall_status = ValidationStatus.WARNING
            else:
                overall_status = ValidationStatus.PASSED
            
            # 保存结果
            self.validation_results[table_name] = results
            self.table_summaries[table_name] = TableValidationSummary(
                table_name=table_name,
                total_checks=len(results),
                passed_checks=passed,
                failed_checks=failed,
                warning_checks=warnings,
                overall_status=overall_status,
                validation_time=time.time() - start_time,
                results=results
            )
            
            # 进度回调
            if self.progress_callback:
                await self.progress_callback(table_name, overall_status)
            
            logger.debug(f"表 {table_name} 验证完成: {passed} 通过, {failed} 失败, {warnings} 警告")
            
        except Exception as e:
            error_result = ValidationResult(
                table_name=table_name,
                validation_type="table_validation",
                status=ValidationStatus.FAILED,
                message=f"表验证失败: {str(e)}"
            )
            
            self.validation_results[table_name] = [error_result]
            self.table_summaries[table_name] = TableValidationSummary(
                table_name=table_name,
                total_checks=1,
                passed_checks=0,
                failed_checks=1,
                warning_checks=0,
                overall_status=ValidationStatus.FAILED,
                validation_time=time.time() - start_time,
                results=[error_result]
            )
            
            logger.error(f"表 {table_name} 验证出错: {str(e)}")
    
    async def _validate_row_count(self, table_name: str) -> List[ValidationResult]:
        """验证行数"""
        results = []
        
        try:
            source_pool = await pool_manager.get_async_pool(self.source_config, self.pool_config)
            target_pool = await pool_manager.get_async_pool(self.target_config, self.pool_config)
            
            # 获取源表行数
            async with source_pool.get_connection() as conn:
                source_result = await conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                source_count = source_result[0][0] if source_result else 0
            
            # 获取目标表行数
            async with target_pool.get_connection() as conn:
                target_result = await conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                target_count = target_result[0][0] if target_result else 0
            
            # 比较行数
            if source_count == target_count:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="row_count",
                    status=ValidationStatus.PASSED,
                    message=f"行数匹配: {source_count} 行",
                    details={"source_count": source_count, "target_count": target_count}
                ))
            else:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="row_count",
                    status=ValidationStatus.FAILED,
                    message=f"行数不匹配: 源表 {source_count} 行, 目标表 {target_count} 行",
                    details={"source_count": source_count, "target_count": target_count}
                ))
            
        except Exception as e:
            results.append(ValidationResult(
                table_name=table_name,
                validation_type="row_count",
                status=ValidationStatus.FAILED,
                message=f"行数验证失败: {str(e)}"
            ))
        
        return results
    
    async def _validate_table_structure(self, table_name: str) -> List[ValidationResult]:
        """验证表结构"""
        results = []
        
        try:
            source_pool = await pool_manager.get_async_pool(self.source_config, self.pool_config)
            target_pool = await pool_manager.get_async_pool(self.target_config, self.pool_config)
            
            # 获取源表结构
            source_columns = await self._get_table_columns(source_pool, table_name, self.source_config.db_type)
            
            # 获取目标表结构
            target_columns = await self._get_table_columns(target_pool, table_name, self.target_config.db_type)
            
            # 比较列数
            if len(source_columns) == len(target_columns):
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="column_count",
                    status=ValidationStatus.PASSED,
                    message=f"列数匹配: {len(source_columns)} 列"
                ))
            else:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="column_count",
                    status=ValidationStatus.FAILED,
                    message=f"列数不匹配: 源表 {len(source_columns)} 列, 目标表 {len(target_columns)} 列"
                ))
            
            # 比较列名
            source_column_names = {col['name'] for col in source_columns}
            target_column_names = {col['name'] for col in target_columns}
            
            missing_in_target = source_column_names - target_column_names
            extra_in_target = target_column_names - source_column_names
            
            if not missing_in_target and not extra_in_target:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="column_names",
                    status=ValidationStatus.PASSED,
                    message="列名完全匹配"
                ))
            else:
                message_parts = []
                if missing_in_target:
                    message_parts.append(f"目标表缺少列: {', '.join(missing_in_target)}")
                if extra_in_target:
                    message_parts.append(f"目标表多余列: {', '.join(extra_in_target)}")
                
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="column_names",
                    status=ValidationStatus.FAILED,
                    message="; ".join(message_parts),
                    details={
                        "missing_in_target": list(missing_in_target),
                        "extra_in_target": list(extra_in_target)
                    }
                ))
            
        except Exception as e:
            results.append(ValidationResult(
                table_name=table_name,
                validation_type="table_structure",
                status=ValidationStatus.FAILED,
                message=f"表结构验证失败: {str(e)}"
            ))
        
        return results
    
    async def _validate_data_types(self, table_name: str) -> List[ValidationResult]:
        """验证数据类型"""
        results = []
        
        try:
            source_pool = await pool_manager.get_async_pool(self.source_config, self.pool_config)
            target_pool = await pool_manager.get_async_pool(self.target_config, self.pool_config)
            
            # 获取列信息
            source_columns = await self._get_table_columns(source_pool, table_name, self.source_config.db_type)
            target_columns = await self._get_table_columns(target_pool, table_name, self.target_config.db_type)
            
            # 创建列映射
            source_col_map = {col['name']: col for col in source_columns}
            target_col_map = {col['name']: col for col in target_columns}
            
            # 比较数据类型
            type_mismatches = []
            for col_name in source_col_map:
                if col_name in target_col_map:
                    source_type = source_col_map[col_name].get('type', '').lower()
                    target_type = target_col_map[col_name].get('type', '').lower()
                    
                    # 简化类型比较（处理不同数据库的类型差异）
                    if not self._types_compatible(source_type, target_type):
                        type_mismatches.append({
                            'column': col_name,
                            'source_type': source_type,
                            'target_type': target_type
                        })
            
            if not type_mismatches:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="data_types",
                    status=ValidationStatus.PASSED,
                    message="数据类型匹配"
                ))
            else:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="data_types",
                    status=ValidationStatus.WARNING,
                    message=f"发现 {len(type_mismatches)} 个数据类型差异",
                    details={"mismatches": type_mismatches}
                ))
            
        except Exception as e:
            results.append(ValidationResult(
                table_name=table_name,
                validation_type="data_types",
                status=ValidationStatus.FAILED,
                message=f"数据类型验证失败: {str(e)}"
            ))
        
        return results
    
    async def _validate_constraints(self, table_name: str) -> List[ValidationResult]:
        """验证约束"""
        results = []
        
        try:
            # 这里可以添加主键、外键、唯一约束等验证
            # 简化实现，只检查是否存在主键
            
            source_pool = await pool_manager.get_async_pool(self.source_config, self.pool_config)
            target_pool = await pool_manager.get_async_pool(self.target_config, self.pool_config)
            
            source_pk = await self._get_primary_key(source_pool, table_name, self.source_config.db_type)
            target_pk = await self._get_primary_key(target_pool, table_name, self.target_config.db_type)
            
            if source_pk == target_pk:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="primary_key",
                    status=ValidationStatus.PASSED,
                    message="主键约束匹配"
                ))
            else:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="primary_key",
                    status=ValidationStatus.WARNING,
                    message="主键约束不匹配",
                    details={"source_pk": source_pk, "target_pk": target_pk}
                ))
            
        except Exception as e:
            results.append(ValidationResult(
                table_name=table_name,
                validation_type="constraints",
                status=ValidationStatus.FAILED,
                message=f"约束验证失败: {str(e)}"
            ))
        
        return results
    
    async def _validate_indexes(self, table_name: str) -> List[ValidationResult]:
        """验证索引"""
        results = []
        
        try:
            # 简化实现，只验证索引数量
            source_pool = await pool_manager.get_async_pool(self.source_config, self.pool_config)
            target_pool = await pool_manager.get_async_pool(self.target_config, self.pool_config)
            
            source_indexes = await self._get_table_indexes(source_pool, table_name, self.source_config.db_type)
            target_indexes = await self._get_table_indexes(target_pool, table_name, self.target_config.db_type)
            
            if len(source_indexes) == len(target_indexes):
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="indexes",
                    status=ValidationStatus.PASSED,
                    message=f"索引数量匹配: {len(source_indexes)} 个"
                ))
            else:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="indexes",
                    status=ValidationStatus.WARNING,
                    message=f"索引数量不匹配: 源表 {len(source_indexes)} 个, 目标表 {len(target_indexes)} 个"
                ))
            
        except Exception as e:
            results.append(ValidationResult(
                table_name=table_name,
                validation_type="indexes",
                status=ValidationStatus.FAILED,
                message=f"索引验证失败: {str(e)}"
            ))
        
        return results
    
    async def _validate_data_content(self, table_name: str) -> List[ValidationResult]:
        """验证数据内容（校验和）"""
        results = []
        
        try:
            source_pool = await pool_manager.get_async_pool(self.source_config, self.pool_config)
            target_pool = await pool_manager.get_async_pool(self.target_config, self.pool_config)
            
            # 计算数据校验和
            source_checksum = await self._calculate_table_checksum(source_pool, table_name)
            target_checksum = await self._calculate_table_checksum(target_pool, table_name)
            
            if source_checksum == target_checksum:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="data_checksum",
                    status=ValidationStatus.PASSED,
                    message="数据内容校验和匹配",
                    details={"checksum": source_checksum}
                ))
            else:
                results.append(ValidationResult(
                    table_name=table_name,
                    validation_type="data_checksum",
                    status=ValidationStatus.FAILED,
                    message="数据内容校验和不匹配",
                    details={
                        "source_checksum": source_checksum,
                        "target_checksum": target_checksum
                    }
                ))
            
        except Exception as e:
            results.append(ValidationResult(
                table_name=table_name,
                validation_type="data_content",
                status=ValidationStatus.FAILED,
                message=f"数据内容验证失败: {str(e)}"
            ))
        
        return results
    
    async def _get_common_tables(self) -> List[str]:
        """获取源和目标数据库的共同表"""
        source_pool = await pool_manager.get_async_pool(self.source_config, self.pool_config)
        target_pool = await pool_manager.get_async_pool(self.target_config, self.pool_config)
        
        # 获取源表列表
        source_tables = await self._get_table_list(source_pool, self.source_config.db_type)
        
        # 获取目标表列表
        target_tables = await self._get_table_list(target_pool, self.target_config.db_type)
        
        # 返回共同表
        return list(set(source_tables) & set(target_tables))
    
    async def _get_table_list(self, pool, db_type: str) -> List[str]:
        """获取表列表"""
        async with pool.get_connection() as conn:
            if db_type == 'mysql':
                result = await conn.execute("SHOW TABLES")
                return [row[0] for row in result]
            elif db_type == 'postgresql':
                result = await conn.execute(
                    "SELECT tablename FROM pg_tables WHERE schemaname = 'public'"
                )
                return [row[0] for row in result]
            else:
                raise ValueError(f"不支持的数据库类型: {db_type}")
    
    async def _get_table_columns(self, pool, table_name: str, db_type: str) -> List[Dict[str, Any]]:
        """获取表列信息"""
        async with pool.get_connection() as conn:
            if db_type == 'mysql':
                result = await conn.execute(f"DESCRIBE {table_name}")
                return [
                    {
                        'name': row[0],
                        'type': row[1],
                        'nullable': row[2] == 'YES',
                        'default': row[4]
                    }
                    for row in result
                ]
            elif db_type == 'postgresql':
                result = await conn.execute(f"""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = '{table_name}'
                    ORDER BY ordinal_position
                """)
                return [
                    {
                        'name': row[0],
                        'type': row[1],
                        'nullable': row[2] == 'YES',
                        'default': row[3]
                    }
                    for row in result
                ]
            else:
                raise ValueError(f"不支持的数据库类型: {db_type}")
    
    async def _get_primary_key(self, pool, table_name: str, db_type: str) -> List[str]:
        """获取主键列"""
        async with pool.get_connection() as conn:
            if db_type == 'mysql':
                result = await conn.execute(f"""
                    SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE TABLE_NAME = '{table_name}' AND CONSTRAINT_NAME = 'PRIMARY'
                    ORDER BY ORDINAL_POSITION
                """)
                return [row[0] for row in result]
            elif db_type == 'postgresql':
                result = await conn.execute(f"""
                    SELECT a.attname
                    FROM pg_index i
                    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
                    WHERE i.indrelid = '{table_name}'::regclass AND i.indisprimary
                """)
                return [row[0] for row in result]
            else:
                return []
    
    async def _get_table_indexes(self, pool, table_name: str, db_type: str) -> List[str]:
        """获取表索引"""
        async with pool.get_connection() as conn:
            if db_type == 'mysql':
                result = await conn.execute(f"SHOW INDEX FROM {table_name}")
                return list(set(row[2] for row in result))  # 索引名去重
            elif db_type == 'postgresql':
                result = await conn.execute(f"""
                    SELECT indexname FROM pg_indexes WHERE tablename = '{table_name}'
                """)
                return [row[0] for row in result]
            else:
                return []
    
    async def _calculate_table_checksum(self, pool, table_name: str) -> str:
        """计算表数据校验和"""
        async with pool.get_connection() as conn:
            # 简化实现：对所有数据进行MD5校验
            result = await conn.execute(f"SELECT * FROM {table_name} ORDER BY 1")
            
            # 将所有数据转换为字符串并计算哈希
            data_str = json.dumps([list(row) for row in result], sort_keys=True, default=str)
            return hashlib.md5(data_str.encode()).hexdigest()
    
    def _types_compatible(self, source_type: str, target_type: str) -> bool:
        """检查数据类型兼容性"""
        # 简化的类型兼容性检查
        type_mappings = {
            'int': ['int', 'integer', 'bigint', 'smallint'],
            'varchar': ['varchar', 'text', 'char'],
            'datetime': ['datetime', 'timestamp'],
            'decimal': ['decimal', 'numeric', 'float', 'double']
        }
        
        for base_type, compatible_types in type_mappings.items():
            if any(t in source_type for t in compatible_types) and any(t in target_type for t in compatible_types):
                return True
        
        return source_type == target_type
    
    def set_progress_callback(self, callback: callable):
        """设置进度回调"""
        self.progress_callback = callback
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证摘要"""
        if not self.table_summaries:
            return {}
        
        total_tables = len(self.table_summaries)
        passed_tables = len([s for s in self.table_summaries.values() if s.overall_status == ValidationStatus.PASSED])
        failed_tables = len([s for s in self.table_summaries.values() if s.overall_status == ValidationStatus.FAILED])
        warning_tables = len([s for s in self.table_summaries.values() if s.overall_status == ValidationStatus.WARNING])
        
        total_checks = sum(s.total_checks for s in self.table_summaries.values())
        passed_checks = sum(s.passed_checks for s in self.table_summaries.values())
        failed_checks = sum(s.failed_checks for s in self.table_summaries.values())
        warning_checks = sum(s.warning_checks for s in self.table_summaries.values())
        
        return {
            'validation_level': self.validation_level.value,
            'total_tables': total_tables,
            'passed_tables': passed_tables,
            'failed_tables': failed_tables,
            'warning_tables': warning_tables,
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'failed_checks': failed_checks,
            'warning_checks': warning_checks,
            'success_rate': (passed_checks / total_checks * 100) if total_checks > 0 else 0,
            'table_summaries': {name: summary.__dict__ for name, summary in self.table_summaries.items()}
        }
