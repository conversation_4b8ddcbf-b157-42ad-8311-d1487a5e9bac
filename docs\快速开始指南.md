# RDS 数据库迁移工具 - 快速开始指南

## 🎯 5分钟快速上手

本指南将帮助您在5分钟内完成第一次数据库迁移。

## 📋 准备工作

### 1. 确认环境要求
- ✅ Python 3.8 或更高版本
- ✅ 网络连接到源和目标数据库
- ✅ 数据库用户权限 (读取源库，写入目标库)

### 2. 快速安装
```bash
# 克隆项目
git clone <repository-url>
cd rds-migrate

# 安装依赖 (使用国内镜像)
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 验证安装
python -m rds_migrate.cli --help
```

## 🚀 第一次迁移

### 步骤1: 创建配置文件

```bash
# 生成配置模板
python -m rds_migrate.cli init --name my_first_migration
```

这将创建 `my_first_migration_config.yaml` 文件。

### 步骤2: 编辑配置

打开配置文件，填入您的数据库信息：

```yaml
# 源数据库 (您要迁移的数据库)
source:
  host: "源数据库地址"
  port: 3306
  username: "源数据库用户名"
  password: "源数据库密码"
  database: "源数据库名"
  db_type: "mysql"

# 目标数据库 (迁移到的数据库)
target:
  host: "目标数据库地址"
  port: 3306
  username: "目标数据库用户名"
  password: "目标数据库密码"
  database: "目标数据库名"
  db_type: "mysql"

# 迁移设置 (保持默认值即可)
batch_size: 1000
parallel_workers: 4
create_indexes: true
create_foreign_keys: true
```

### 步骤3: 测试连接

```bash
# 测试数据库连接
python -m rds_migrate.cli test --config my_first_migration_config.yaml
```

如果看到 ✅ 连接成功的消息，继续下一步。

### 步骤4: 分析数据库

```bash
# 查看源数据库信息
python -m rds_migrate.cli analyze --config my_first_migration_config.yaml
```

这会显示数据库中的表、行数等信息。

### 步骤5: 执行迁移

```bash
# 开始迁移
python -m rds_migrate.cli migrate --config my_first_migration_config.yaml --progress
```

🎉 **恭喜！** 您已经完成了第一次数据库迁移！

## 🔧 常用场景配置

### 场景1: MySQL 到 PostgreSQL

```yaml
source:
  host: "mysql-server.com"
  port: 3306
  username: "mysql_user"
  password: "mysql_pass"
  database: "app_db"
  db_type: "mysql"
  charset: "utf8mb4"

target:
  host: "postgres-server.com"
  port: 5432
  username: "postgres_user"
  password: "postgres_pass"
  database: "app_db"
  db_type: "postgresql"
```

### 场景2: 本地数据库到云RDS

```yaml
source:
  host: "localhost"
  port: 3306
  username: "root"
  password: "local_password"
  database: "my_app"
  db_type: "mysql"

target:
  host: "myapp.cluster-xyz.us-east-1.rds.amazonaws.com"
  port: 3306
  username: "admin"
  password: "rds_password"
  database: "my_app"
  db_type: "mysql"
```

### 场景3: 只迁移特定表

```yaml
# ... 数据库配置 ...

# 只迁移指定的表
tables:
  - "users"
  - "orders"
  - "products"

# 排除不需要的表
exclude_tables:
  - "temp_data"
  - "logs"
  - "cache"
```

### 场景4: 只迁移结构或数据

```yaml
# 只迁移表结构，不迁移数据
schema_only: true
data_only: false

# 或者只迁移数据，不创建表结构
schema_only: false
data_only: true
```

## 🌐 使用Web界面

如果您更喜欢图形界面：

```bash
# 启动Web界面
streamlit run web_app.py
```

然后在浏览器中访问 `http://localhost:8501`

### Web界面功能
- 📝 **可视化配置** - 通过表单创建配置
- 🔗 **连接测试** - 一键测试数据库连接
- 📊 **实时监控** - 查看迁移进度
- 📋 **日志查看** - 实时查看迁移日志

## ⚡ 性能优化技巧

### 小数据库 (< 1GB)
```yaml
batch_size: 1000
parallel_workers: 2
```

### 中等数据库 (1-10GB)
```yaml
batch_size: 5000
parallel_workers: 4
```

### 大数据库 (> 10GB)
```yaml
batch_size: 10000
parallel_workers: 8
```

## 🛡️ 安全最佳实践

### 1. 使用环境变量存储密码

```bash
# 设置环境变量
export SOURCE_PASSWORD="your_source_password"
export TARGET_PASSWORD="your_target_password"
```

```yaml
# 在配置文件中引用
source:
  password: "${SOURCE_PASSWORD}"
target:
  password: "${TARGET_PASSWORD}"
```

### 2. 使用只读用户连接源数据库

```sql
-- MySQL
CREATE USER 'migration_reader'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT ON source_database.* TO 'migration_reader'@'%';

-- PostgreSQL
CREATE USER migration_reader WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE source_database TO migration_reader;
GRANT USAGE ON SCHEMA public TO migration_reader;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO migration_reader;
```

## 🚨 常见问题快速解决

### 问题1: 连接失败
```bash
# 检查网络连通性
ping 数据库地址
telnet 数据库地址 端口号

# 检查配置文件
python -m rds_migrate.cli validate --config config.yaml
```

### 问题2: 权限不足
```sql
-- 检查用户权限
SHOW GRANTS FOR 'username'@'host';  -- MySQL
\du username                        -- PostgreSQL
```

### 问题3: 内存不足
```yaml
# 减小批次大小
batch_size: 500
parallel_workers: 2
```

### 问题4: 迁移速度慢
```yaml
# 增加批次大小和并行度
batch_size: 5000
parallel_workers: 6
```

## 📚 下一步学习

完成第一次迁移后，您可以：

1. 📖 **阅读详细文档**
   - [用户使用指南](用户使用指南.md) - 完整的使用说明
   - [技术架构文档](技术架构文档.md) - 了解系统架构
   - [API文档](API文档.md) - 程序化使用

2. 🔧 **探索高级功能**
   - 自定义数据转换
   - 批量迁移多个数据库
   - 集成到CI/CD流程

3. 🛠️ **故障排除**
   - [故障排除指南](故障排除指南.md) - 解决常见问题

4. 🤝 **参与社区**
   - 报告问题和建议
   - 贡献代码和文档
   - 分享使用经验

## 📞 获取帮助

如果遇到问题：

1. 📖 **查看文档** - 检查相关文档是否有解决方案
2. 🔍 **搜索问题** - 在GitHub Issues中搜索类似问题
3. 🐛 **报告问题** - 提交新的Issue并提供详细信息
4. 💬 **社区讨论** - 在GitHub Discussions中寻求帮助

## 🎯 成功案例

### 案例1: 电商网站迁移
- **场景**: MySQL 5.7 → PostgreSQL 13
- **数据量**: 50GB, 200个表
- **耗时**: 2小时
- **配置**: batch_size=5000, parallel_workers=6

### 案例2: 企业ERP系统
- **场景**: SQL Server → MySQL (云RDS)
- **数据量**: 100GB, 500个表
- **耗时**: 4小时
- **配置**: batch_size=10000, parallel_workers=8

### 案例3: 微服务拆分
- **场景**: 单体MySQL → 多个PostgreSQL微服务
- **数据量**: 20GB, 按业务模块拆分
- **耗时**: 1小时
- **配置**: 使用表过滤功能分别迁移

---

🎉 **开始您的数据库迁移之旅吧！**

*本文档最后更新: 2025-01-26*
