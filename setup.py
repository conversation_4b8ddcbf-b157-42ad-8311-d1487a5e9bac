"""
Setup script for RDS Migration Tool.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="rds-migrate",
    version="1.0.0",
    author="RDS Migration Tool Team",
    author_email="<EMAIL>",
    description="A comprehensive tool for migrating databases between traditional DB and cloud RDS",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/rds-migrate",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Database",
        "Topic :: System :: Systems Administration",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "rds-migrate=rds_migrate.cli:cli",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
