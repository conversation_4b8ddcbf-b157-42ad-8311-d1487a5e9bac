# Kubernetes Secrets配置
# 注意：这些是示例值，生产环境中应使用真实的安全凭据

apiVersion: v1
kind: Secret
metadata:
  name: rds-migrate-secrets
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
type: Opaque
data:
  # PostgreSQL连接信息 (base64编码)
  postgres-password: cG9zdGdyZXM=  # postgres
  postgres-url: ****************************************************************************************
  
  # Redis连接信息
  redis-password: cmVkaXNfcGFzc3dvcmQ=  # redis_password
  redis-url: cmVkaXM6Ly86cmVkaXNfcGFzc3dvcmRAcmVkaXMtc2VydmljZTo2Mzc5LzA=
  
  # 应用密钥
  app-secret-key: c3VwZXJfc2VjcmV0X2tleV9mb3JfcHJvZHVjdGlvbg==  # super_secret_key_for_production
  encryption-key: ZW5jcnlwdGlvbl9rZXlfZm9yX3NlbnNpdGl2ZV9kYXRh  # encryption_key_for_sensitive_data
  
  # JWT密钥
  jwt-secret: and0X3NlY3JldF9rZXlfZm9yX2F1dGhlbnRpY2F0aW9u  # jwt_secret_key_for_authentication

---
# 基础认证密钥（用于管理界面）
apiVersion: v1
kind: Secret
metadata:
  name: rds-migrate-basic-auth
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: auth
type: Opaque
data:
  # admin:admin123 (htpasswd格式)
  auth: YWRtaW46JGFwcjEkSDZ1ek5wVE8kSWdVaWdGa3FtUE9zTGJuMWZOdkZOMQ==

---
# TLS证书密钥（示例，生产环境应使用cert-manager自动管理）
apiVersion: v1
kind: Secret
metadata:
  name: rds-migrate-tls
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: tls
type: kubernetes.io/tls
data:
  # 这里应该是实际的TLS证书和私钥（base64编码）
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...

---
# 数据库连接密钥模板（用于实际的源和目标数据库）
apiVersion: v1
kind: Secret
metadata:
  name: rds-migrate-db-connections
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: database
type: Opaque
data:
  # 源数据库连接信息（示例）
  source-mysql-host: c291cmNlLW15c3FsLmV4YW1wbGUuY29t  # source-mysql.example.com
  source-mysql-port: MzMwNg==  # 3306
  source-mysql-user: bXlzcWxfdXNlcg==  # mysql_user
  source-mysql-password: bXlzcWxfcGFzc3dvcmQ=  # mysql_password
  source-mysql-database: c291cmNlX2Ri  # source_db
  
  # 目标数据库连接信息（示例）
  target-postgres-host: dGFyZ2V0LXBvc3RncmVzLmV4YW1wbGUuY29t  # target-postgres.example.com
  target-postgres-port: NTQzMg==  # 5432
  target-postgres-user: cG9zdGdyZXNfdXNlcg==  # postgres_user
  target-postgres-password: cG9zdGdyZXNfcGFzc3dvcmQ=  # postgres_password
  target-postgres-database: dGFyZ2V0X2Ri  # target_db
