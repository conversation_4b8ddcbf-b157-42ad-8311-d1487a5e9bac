# Kubernetes Deployment配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rds-migrate-app
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
    component: app
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rds-migration-tool
      component: app
  template:
    metadata:
      labels:
        app: rds-migration-tool
        component: app
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: rds-migrate-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      initContainers:
      - name: init-db
        image: postgres:15-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h postgres-service -p 5432 -U postgres; do
            echo "等待PostgreSQL就绪..."
            sleep 2
          done
          echo "PostgreSQL已就绪"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: rds-migrate-secrets
              key: postgres-password
      containers:
      - name: rds-migrate
        image: rds-migrate:2.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        - containerPort: 8080
          name: api
          protocol: TCP
        env:
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: RDS_MIGRATE_ENV
          value: "production"
        - name: RDS_MIGRATE_CONFIG_DIR
          value: "/app/config"
        - name: RDS_MIGRATE_DATA_DIR
          value: "/app/data"
        - name: RDS_MIGRATE_LOG_DIR
          value: "/app/logs"
        - name: POSTGRES_URL
          valueFrom:
            secretKeyRef:
              name: rds-migrate-secrets
              key: postgres-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: rds-migrate-secrets
              key: redis-url
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
        - name: backups-volume
          mountPath: /app/backups
        - name: scripts-volume
          mountPath: /app/scripts
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: config-volume
        configMap:
          name: rds-migrate-config
          items:
          - key: app.yaml
            path: app.yaml
      - name: data-volume
        persistentVolumeClaim:
          claimName: rds-migrate-data-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: rds-migrate-logs-pvc
      - name: backups-volume
        persistentVolumeClaim:
          claimName: rds-migrate-backups-pvc
      - name: scripts-volume
        configMap:
          name: rds-migrate-scripts
          defaultMode: 0755

---
# Redis Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: rds-migrate
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rds-migrate-secrets
              key: redis-password
        volumeMounts:
        - name: redis-data
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-data-pvc

---
# PostgreSQL Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: rds-migrate
  labels:
    app: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "rds_migrate"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rds-migrate-secrets
              key: postgres-password
        - name: POSTGRES_INITDB_ARGS
          value: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: init-scripts
          mountPath: /docker-entrypoint-initdb.d
          readOnly: true
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-data-pvc
      - name: init-scripts
        configMap:
          name: postgres-init-scripts
