# Example: Schema-only migration
# This configuration migrates only the database structure without data

source:
  host: "production-db.company.com"
  port: 3306
  database: "prod_db"
  username: "readonly_user"
  password: "readonly_password"
  db_type: "mysql"

target:
  host: "staging-rds.amazonaws.com"
  port: 3306
  database: "staging_db"
  username: "admin"
  password: "admin_password"
  db_type: "mysql"

migration:
  # Migrate all tables structure
  tables: null
  exclude_tables: null
  
  # Not relevant for schema-only migration
  batch_size: 1000
  parallel_workers: 1
  
  # Create complete schema structure
  create_indexes: true
  create_foreign_keys: true
  
  # Schema only - no data migration
  data_only: false
  schema_only: true
