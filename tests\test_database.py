"""
数据库管理模块测试
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import sqlalchemy
from sqlalchemy.exc import SQLAlchemyError, OperationalError

from rds_migrate.database import DatabaseManager
from rds_migrate.config import DatabaseConfig


@pytest.mark.unit
@pytest.mark.database
class TestDatabaseManager:
    """数据库管理器测试"""
    
    def test_database_manager_creation(self, database_config):
        """测试数据库管理器创建"""
        manager = DatabaseManager(database_config)

        assert manager.config == database_config
        assert manager.engine is None
        assert manager._connection_string is not None

    def test_build_connection_string_mysql(self):
        """测试MySQL连接字符串构建"""
        config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="test_db",
            username="user",
            password="pass",
            charset="utf8mb4"
        )

        manager = DatabaseManager(config)
        expected = "mysql+pymysql://user:pass@localhost:3306/test_db?charset=utf8mb4"
        assert manager._connection_string == expected

    def test_build_connection_string_postgresql(self):
        """测试PostgreSQL连接字符串构建"""
        config = DatabaseConfig(
            db_type="postgresql",
            host="localhost",
            port=5432,
            database="test_db",
            username="user",
            password="pass",
            ssl_mode="require"
        )

        manager = DatabaseManager(config)
        expected = "postgresql+psycopg2://user:pass@localhost:5432/test_db?sslmode=require"
        assert manager._connection_string == expected

    @patch('rds_migrate.database.create_engine')
    def test_connect_success(self, mock_create_engine, database_config):
        """测试成功连接数据库"""
        mock_engine = Mock()
        mock_connection = Mock()
        mock_engine.connect.return_value.__enter__ = Mock(return_value=mock_connection)
        mock_engine.connect.return_value.__exit__ = Mock(return_value=None)
        mock_connection.execute.return_value = None
        mock_create_engine.return_value = mock_engine

        manager = DatabaseManager(database_config)
        result = manager.connect()

        assert result == mock_engine
        assert manager.engine == mock_engine
        mock_create_engine.assert_called_once()

    @patch('rds_migrate.database.create_engine')
    def test_connect_failure(self, mock_create_engine, database_config):
        """测试连接数据库失败"""
        from rds_migrate.database import DatabaseConnectionError
        mock_create_engine.side_effect = SQLAlchemyError("Connection failed")

        manager = DatabaseManager(database_config)

        with pytest.raises(DatabaseConnectionError):
            manager.connect()

        assert manager.engine is None
        assert manager.engine is None

    def test_disconnect(self, database_config):
        """测试断开数据库连接"""
        manager = DatabaseManager(database_config)
        mock_engine = Mock()
        manager.engine = mock_engine

        manager.disconnect()

        mock_engine.dispose.assert_called_once()
        assert manager.engine is None

    def test_disconnect_no_engine(self, database_config):
        """测试没有引擎时断开连接"""
        manager = DatabaseManager(database_config)

        # 应该不抛出异常
        manager.disconnect()
        assert manager.engine is None

    @patch('rds_migrate.database.DatabaseManager.get_connection')
    def test_execute_query_success(self, mock_get_connection, database_config):
        """测试成功执行查询"""
        mock_connection = Mock()
        mock_result = Mock()
        mock_connection.execute.return_value = mock_result
        mock_get_connection.return_value.__enter__ = Mock(return_value=mock_connection)
        mock_get_connection.return_value.__exit__ = Mock(return_value=None)

        manager = DatabaseManager(database_config)
        result = manager.execute_query("SELECT * FROM test")

        assert result == mock_result
        mock_connection.execute.assert_called_once()

    @patch('rds_migrate.database.DatabaseManager.get_connection')
    def test_execute_query_failure(self, mock_get_connection, database_config):
        """测试执行查询失败"""
        mock_connection = Mock()
        mock_connection.execute.side_effect = SQLAlchemyError("Query failed")
        mock_get_connection.return_value.__enter__ = Mock(return_value=mock_connection)
        mock_get_connection.return_value.__exit__ = Mock(return_value=None)

        manager = DatabaseManager(database_config)

        with pytest.raises(Exception):
            manager.execute_query("SELECT * FROM test")
    
    @patch('rds_migrate.database.DatabaseManager.get_connection')
    def test_get_table_list_mock(self, mock_get_connection, database_config):
        """测试获取表列表"""
        mock_connection = Mock()
        mock_metadata = Mock()
        mock_metadata.tables.keys.return_value = ["users", "orders", "products"]

        with patch('rds_migrate.database.MetaData', return_value=mock_metadata):
            mock_get_connection.return_value.__enter__ = Mock(return_value=mock_connection)
            mock_get_connection.return_value.__exit__ = Mock(return_value=None)

            manager = DatabaseManager(database_config)
            tables = manager.get_table_list()

            assert tables == ["users", "orders", "products"]
    
    @patch('rds_migrate.database.DatabaseManager.execute_query')
    def test_get_row_count_mock(self, mock_execute_query, database_config):
        """测试获取表行数"""
        mock_result = Mock()
        mock_result.scalar.return_value = 1000
        mock_execute_query.return_value = mock_result

        manager = DatabaseManager(database_config)
        count = manager.get_row_count("users")

        assert count == 1000
    
    def test_get_connection_context_manager(self, database_config):
        """测试get_connection上下文管理器"""
        with patch('rds_migrate.database.create_engine') as mock_create_engine:
            mock_engine = Mock()
            mock_connection = Mock()
            mock_engine.connect.return_value = mock_connection
            mock_create_engine.return_value = mock_engine

            manager = DatabaseManager(database_config)
            manager.engine = mock_engine

            with manager.get_connection() as conn:
                assert conn == mock_connection

            # 验证连接被关闭
            mock_connection.close.assert_called_once()

    def test_connection_string_property(self, database_config):
        """测试连接字符串属性"""
        manager = DatabaseManager(database_config)

        expected = f"{database_config.db_type}+pymysql://{database_config.username}:{database_config.password}@{database_config.host}:{database_config.port}/{database_config.database}?charset={database_config.charset}"
        assert manager._connection_string == expected
    
    @patch('rds_migrate.database.DatabaseManager.get_connection')
    @patch('rds_migrate.database.DatabaseManager.connect')
    def test_test_connection_success(self, mock_connect, mock_get_connection, database_config):
        """测试连接测试成功"""
        mock_connection = Mock()
        mock_get_connection.return_value.__enter__ = Mock(return_value=mock_connection)
        mock_get_connection.return_value.__exit__ = Mock(return_value=None)
        mock_connection.execute.return_value = None

        manager = DatabaseManager(database_config)
        result = manager.test_connection()

        assert result == True
    
    def test_test_connection_failure(self, database_config):
        """测试连接测试失败"""
        with patch('rds_migrate.database.create_engine') as mock_create_engine:
            mock_create_engine.side_effect = OperationalError("Connection failed", None, None)
            
            manager = DatabaseManager(database_config)
            result = manager.test_connection()
            
            assert result == False
