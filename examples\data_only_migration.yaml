# Example: Data-only migration
# This configuration migrates only data, assuming schema already exists

source:
  host: "backup-db.company.com"
  port: 5432
  database: "backup_db"
  username: "backup_user"
  password: "backup_password"
  db_type: "postgresql"

target:
  host: "restored-db.company.com"
  port: 5432
  database: "restored_db"
  username: "restore_user"
  password: "restore_password"
  db_type: "postgresql"

migration:
  # Migrate specific tables data
  tables:
    - "user_data"
    - "transaction_history"
    - "audit_logs"
  
  exclude_tables: null
  
  # Optimized for data transfer
  batch_size: 5000
  parallel_workers: 8
  
  # Not relevant for data-only migration
  create_indexes: false
  create_foreign_keys: false
  
  # Data only - schema must already exist
  data_only: true
  schema_only: false
