"""
简化版命令行界面 - 不依赖外部包
"""

import os
import sys
import argparse
import json
from pathlib import Path
from typing import Optional

from .config import ConfigManager, MigrationConfig, DatabaseConfig
from .database import DatabaseManager
from .migrator import MigrationEngine
from .logger import MigrationLogger, ProgressTracker, MigrationReporter


def simple_table(headers, rows):
    """简单的表格显示函数，替代tabulate"""
    if not rows:
        return "No data"
    
    # 计算列宽
    col_widths = [len(str(h)) for h in headers]
    for row in rows:
        for i, cell in enumerate(row):
            if i < len(col_widths):
                col_widths[i] = max(col_widths[i], len(str(cell)))
    
    # 创建分隔线
    separator = "+" + "+".join("-" * (w + 2) for w in col_widths) + "+"
    
    # 格式化表格
    result = [separator]
    
    # 标题行
    header_row = "|" + "|".join(f" {str(h):<{col_widths[i]}} " for i, h in enumerate(headers)) + "|"
    result.append(header_row)
    result.append(separator)
    
    # 数据行
    for row in rows:
        data_row = "|" + "|".join(f" {str(row[i] if i < len(row) else ''):<{col_widths[i]}} " for i in range(len(headers))) + "|"
        result.append(data_row)
    
    result.append(separator)
    return "\n".join(result)


def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")


def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}", file=sys.stderr)


def print_info(message):
    """打印信息消息"""
    print(f"ℹ️  {message}")


def print_warning(message):
    """打印警告消息"""
    print(f"⚠️  {message}")


def cmd_init(args):
    """初始化示例配置文件"""
    try:
        config_manager = ConfigManager()
        config_manager.create_sample_config(args.output)
        print_success(f"示例配置文件已创建: {args.output}")
        print_info("请在运行迁移前编辑配置文件，填入您的数据库详细信息")
    except Exception as e:
        print_error(f"创建配置文件失败: {str(e)}")
        sys.exit(1)


def cmd_validate(args):
    """验证配置文件"""
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config(args.config)
        print_success("配置文件验证通过")
        
        # 显示配置摘要
        print("\n📋 配置摘要:")
        print(f"源数据库: {config.source.db_type}://{config.source.host}:{config.source.port}/{config.source.database}")
        print(f"目标数据库: {config.target.db_type}://{config.target.host}:{config.target.port}/{config.target.database}")
        print(f"批处理大小: {config.migration.batch_size}")
        print(f"并行工作线程: {config.migration.parallel_workers}")
        
    except Exception as e:
        print_error(f"配置文件验证失败: {str(e)}")
        sys.exit(1)


def cmd_test(args):
    """测试数据库连接"""
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config(args.config)
        
        print_info("测试数据库连接...")
        
        # 测试源数据库
        print("🔍 测试源数据库连接...")
        source_db = DatabaseManager(config.source)
        if source_db.test_connection():
            print_success("源数据库连接成功")
        else:
            print_error("源数据库连接失败")
            return
        
        # 测试目标数据库
        print("🔍 测试目标数据库连接...")
        target_db = DatabaseManager(config.target)
        if target_db.test_connection():
            print_success("目标数据库连接成功")
        else:
            print_error("目标数据库连接失败")
            return
        
        print_success("所有数据库连接测试通过")
        
    except Exception as e:
        print_error(f"连接测试失败: {str(e)}")
        sys.exit(1)


def cmd_analyze(args):
    """分析数据库结构"""
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config(args.config)
        
        print_info("分析源数据库结构...")
        
        source_db = DatabaseManager(config.source)
        tables = source_db.get_tables()
        
        if not tables:
            print_warning("源数据库中没有找到表")
            return
        
        print(f"\n📊 找到 {len(tables)} 个表:")
        
        # 显示表信息
        table_data = []
        for table in tables:
            try:
                row_count = source_db.get_table_row_count(table)
                table_data.append([table, f"{row_count:,}"])
            except Exception as e:
                table_data.append([table, f"错误: {str(e)}"])
        
        print(simple_table(["表名", "行数"], table_data))
        
    except Exception as e:
        print_error(f"数据库分析失败: {str(e)}")
        sys.exit(1)


def cmd_migrate(args):
    """执行数据库迁移"""
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config(args.config)
        
        print_info("开始数据库迁移...")
        print(f"源数据库: {config.source.db_type}://{config.source.host}:{config.source.port}/{config.source.database}")
        print(f"目标数据库: {config.target.db_type}://{config.target.host}:{config.target.port}/{config.target.database}")
        
        # 确认操作
        if not args.yes:
            response = input("\n⚠️  确定要开始迁移吗？这将修改目标数据库。(y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("迁移已取消")
                return
        
        # 创建迁移引擎
        engine = MigrationEngine(config)
        
        # 执行迁移
        print("\n🚀 开始迁移...")
        success = engine.migrate()
        
        if success:
            print_success("数据库迁移完成！")
        else:
            print_error("数据库迁移失败")
            sys.exit(1)
            
    except Exception as e:
        print_error(f"迁移失败: {str(e)}")
        sys.exit(1)


def cmd_status(args):
    """显示迁移状态"""
    try:
        # 这里可以添加状态检查逻辑
        print_info("迁移状态检查功能正在开发中")
    except Exception as e:
        print_error(f"状态检查失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="RDS迁移工具 - 数据库迁移命令行界面",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s init                          # 创建示例配置文件
  %(prog)s init -o my_config.yaml        # 创建指定名称的配置文件
  %(prog)s validate config.yaml          # 验证配置文件
  %(prog)s test config.yaml              # 测试数据库连接
  %(prog)s analyze config.yaml           # 分析数据库结构
  %(prog)s migrate config.yaml           # 执行迁移
  %(prog)s migrate config.yaml -y        # 执行迁移（跳过确认）
        """
    )
    
    parser.add_argument('--version', action='version', version='RDS迁移工具 v1.0.0')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # init命令
    init_parser = subparsers.add_parser('init', help='创建示例配置文件')
    init_parser.add_argument('-o', '--output', default='sample_config.yaml', 
                           help='输出配置文件路径 (默认: sample_config.yaml)')
    
    # validate命令
    validate_parser = subparsers.add_parser('validate', help='验证配置文件')
    validate_parser.add_argument('config', help='配置文件路径')
    
    # test命令
    test_parser = subparsers.add_parser('test', help='测试数据库连接')
    test_parser.add_argument('config', help='配置文件路径')
    
    # analyze命令
    analyze_parser = subparsers.add_parser('analyze', help='分析数据库结构')
    analyze_parser.add_argument('config', help='配置文件路径')
    
    # migrate命令
    migrate_parser = subparsers.add_parser('migrate', help='执行数据库迁移')
    migrate_parser.add_argument('config', help='配置文件路径')
    migrate_parser.add_argument('-y', '--yes', action='store_true', 
                              help='跳过确认提示')
    
    # status命令
    status_parser = subparsers.add_parser('status', help='显示迁移状态')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 执行对应的命令
    command_map = {
        'init': cmd_init,
        'validate': cmd_validate,
        'test': cmd_test,
        'analyze': cmd_analyze,
        'migrate': cmd_migrate,
        'status': cmd_status
    }
    
    if args.command in command_map:
        command_map[args.command](args)
    else:
        print_error(f"未知命令: {args.command}")
        parser.print_help()
        sys.exit(1)


if __name__ == '__main__':
    main()
