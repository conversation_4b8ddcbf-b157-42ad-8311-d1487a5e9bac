# Kubernetes RBAC配置
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rds-migrate-sa
  namespace: rds-migrate
  labels:
    app: rds-migration-tool

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rds-migrate-role
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods/log"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rds-migrate-rolebinding
  namespace: rds-migrate
  labels:
    app: rds-migration-tool
subjects:
- kind: ServiceAccount
  name: rds-migrate-sa
  namespace: rds-migrate
roleRef:
  kind: Role
  name: rds-migrate-role
  apiGroup: rbac.authorization.k8s.io

---
# 集群级别权限（如果需要）
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: rds-migrate-cluster-role
  labels:
    app: rds-migration-tool
rules:
- apiGroups: [""]
  resources: ["nodes", "nodes/metrics"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: rds-migrate-cluster-rolebinding
  labels:
    app: rds-migration-tool
subjects:
- kind: ServiceAccount
  name: rds-migrate-sa
  namespace: rds-migrate
roleRef:
  kind: ClusterRole
  name: rds-migrate-cluster-role
  apiGroup: rbac.authorization.k8s.io
