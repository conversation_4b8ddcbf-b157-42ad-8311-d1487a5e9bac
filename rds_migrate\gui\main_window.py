"""
RDS迁移工具主窗口界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
from pathlib import Path
from typing import Optional

from ..config import Config<PERSON>anager, MigrationConfig
from ..database import DatabaseManager
from ..migrator import MigrationEngine
from ..logger import MigrationLogger, ProgressTracker, MigrationReporter

from .config_dialog import ConfigDialog
from .progress_window import ProgressWindow
from .connection_test import ConnectionTestWindow


class MainWindow:
    """RDS迁移工具主窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("RDS数据库迁移工具 v1.0")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 初始化变量
        self.config_manager = ConfigManager()
        self.current_config: Optional[MigrationConfig] = None
        self.config_file_path: Optional[str] = None
        
        # 创建界面
        self.create_menu()
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建配置", command=self.new_config)
        file_menu.add_command(label="打开配置", command=self.open_config)
        file_menu.add_command(label="保存配置", command=self.save_config)
        file_menu.add_command(label="另存为", command=self.save_config_as)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="测试连接", command=self.test_connections)
        tools_menu.add_command(label="分析数据库", command=self.analyze_database)
        tools_menu.add_command(label="验证配置", command=self.validate_config)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # 配置按钮
        ttk.Button(toolbar, text="新建配置", command=self.new_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="打开配置", command=self.open_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 测试按钮
        ttk.Button(toolbar, text="测试连接", command=self.test_connections).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="分析数据库", command=self.analyze_database).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 迁移按钮
        ttk.Button(toolbar, text="开始迁移", command=self.start_migration, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=2)
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 配置页面
        self.create_config_tab()
        
        # 日志页面
        self.create_log_tab()
    
    def create_config_tab(self):
        """创建配置标签页"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="配置管理")
        
        # 配置信息显示
        info_frame = ttk.LabelFrame(config_frame, text="当前配置信息")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建树形视图显示配置
        self.config_tree = ttk.Treeview(info_frame, columns=("value",), show="tree headings")
        self.config_tree.heading("#0", text="配置项")
        self.config_tree.heading("value", text="值")
        self.config_tree.column("#0", width=200)
        self.config_tree.column("value", width=300)
        
        # 添加滚动条
        config_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.config_tree.yview)
        self.config_tree.configure(yscrollcommand=config_scrollbar.set)
        
        self.config_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        config_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置操作按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="编辑配置", command=self.edit_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="验证配置", command=self.validate_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置配置", command=self.reset_config).pack(side=tk.LEFT, padx=5)
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="日志信息")
        
        # 日志显示区域
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(log_button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 进度条（初始隐藏）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_bar, variable=self.progress_var, 
                                          maximum=100, length=200)
        # 不立即显示进度条
    
    def update_status(self, message: str):
        """更新状态栏信息"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def log_message(self, message: str):
        """添加日志信息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def save_log(self):
        """保存日志到文件"""
        filename = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {str(e)}")
    
    def update_config_display(self):
        """更新配置显示"""
        # 清空现有内容
        for item in self.config_tree.get_children():
            self.config_tree.delete(item)
        
        if not self.current_config:
            return
        
        # 添加源数据库配置
        source_item = self.config_tree.insert("", "end", text="源数据库", values=("",))
        self.config_tree.insert(source_item, "end", text="主机", values=(self.current_config.source.host,))
        self.config_tree.insert(source_item, "end", text="端口", values=(self.current_config.source.port,))
        self.config_tree.insert(source_item, "end", text="数据库", values=(self.current_config.source.database,))
        self.config_tree.insert(source_item, "end", text="用户名", values=(self.current_config.source.username,))
        self.config_tree.insert(source_item, "end", text="数据库类型", values=(self.current_config.source.db_type,))
        
        # 添加目标数据库配置
        target_item = self.config_tree.insert("", "end", text="目标数据库", values=("",))
        self.config_tree.insert(target_item, "end", text="主机", values=(self.current_config.target.host,))
        self.config_tree.insert(target_item, "end", text="端口", values=(self.current_config.target.port,))
        self.config_tree.insert(target_item, "end", text="数据库", values=(self.current_config.target.database,))
        self.config_tree.insert(target_item, "end", text="用户名", values=(self.current_config.target.username,))
        self.config_tree.insert(target_item, "end", text="数据库类型", values=(self.current_config.target.db_type,))
        
        # 添加迁移配置
        migration_item = self.config_tree.insert("", "end", text="迁移设置", values=("",))
        self.config_tree.insert(migration_item, "end", text="批次大小", values=(self.current_config.batch_size,))
        self.config_tree.insert(migration_item, "end", text="并行工作线程", values=(self.current_config.parallel_workers,))
        self.config_tree.insert(migration_item, "end", text="创建索引", values=("是" if self.current_config.create_indexes else "否",))
        self.config_tree.insert(migration_item, "end", text="创建外键", values=("是" if self.current_config.create_foreign_keys else "否",))
        
        if self.current_config.tables:
            self.config_tree.insert(migration_item, "end", text="指定表", values=(", ".join(self.current_config.tables),))
        
        if self.current_config.exclude_tables:
            self.config_tree.insert(migration_item, "end", text="排除表", values=(", ".join(self.current_config.exclude_tables),))
        
        # 展开所有项目
        self.config_tree.item(source_item, open=True)
        self.config_tree.item(target_item, open=True)
        self.config_tree.item(migration_item, open=True)
    
    # 菜单和按钮事件处理方法
    def new_config(self):
        """新建配置"""
        dialog = ConfigDialog(self.root)
        if dialog.result:
            self.current_config = dialog.result
            self.config_file_path = None
            self.update_config_display()
            self.update_status("新配置已创建")
            self.log_message("创建了新的迁移配置")
    
    def open_config(self):
        """打开配置文件"""
        filename = filedialog.askopenfilename(
            title="打开配置文件",
            filetypes=[("YAML文件", "*.yaml"), ("YAML文件", "*.yml"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                self.current_config = self.config_manager.load_config(filename)
                self.config_file_path = filename
                self.update_config_display()
                self.update_status(f"已加载配置: {os.path.basename(filename)}")
                self.log_message(f"加载配置文件: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
    
    def save_config(self):
        """保存配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "没有可保存的配置")
            return
        
        if not self.config_file_path:
            self.save_config_as()
            return
        
        try:
            self.config_manager.save_config(self.current_config, self.config_file_path)
            self.update_status("配置已保存")
            self.log_message(f"配置已保存到: {self.config_file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def save_config_as(self):
        """另存配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "没有可保存的配置")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".yaml",
            filetypes=[("YAML文件", "*.yaml"), ("YAML文件", "*.yml"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                self.config_manager.save_config(self.current_config, filename)
                self.config_file_path = filename
                self.update_status(f"配置已保存: {os.path.basename(filename)}")
                self.log_message(f"配置已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def edit_config(self):
        """编辑配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先创建或加载配置")
            return
        
        dialog = ConfigDialog(self.root, self.current_config)
        if dialog.result:
            self.current_config = dialog.result
            self.update_config_display()
            self.update_status("配置已更新")
            self.log_message("配置已更新")
    
    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置当前配置吗？"):
            self.current_config = None
            self.config_file_path = None
            self.update_config_display()
            self.update_status("配置已重置")
            self.log_message("配置已重置")
    
    def validate_config(self):
        """验证配置"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先创建或加载配置")
            return
        
        try:
            self.config_manager.validate_config(self.current_config)
            messagebox.showinfo("成功", "配置验证通过")
            self.log_message("配置验证通过")
        except Exception as e:
            messagebox.showerror("验证失败", f"配置验证失败: {str(e)}")
            self.log_message(f"配置验证失败: {str(e)}")
    
    def test_connections(self):
        """测试数据库连接"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先创建或加载配置")
            return
        
        test_window = ConnectionTestWindow(self.root, self.current_config)
    
    def analyze_database(self):
        """分析数据库"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先创建或加载配置")
            return
        
        # 这里可以添加数据库分析窗口
        messagebox.showinfo("提示", "数据库分析功能正在开发中")
    
    def start_migration(self):
        """开始迁移"""
        if not self.current_config:
            messagebox.showwarning("警告", "请先创建或加载配置")
            return
        
        # 确认开始迁移
        if not messagebox.askyesno("确认迁移", "确定要开始数据库迁移吗？\n\n请确保已经备份了目标数据库！"):
            return
        
        # 打开进度窗口
        progress_window = ProgressWindow(self.root, self.current_config)
    
    def show_help(self):
        """显示帮助"""
        help_text = """
RDS数据库迁移工具使用说明

1. 创建配置：点击"新建配置"按钮，填写源数据库和目标数据库信息
2. 测试连接：配置完成后，使用"测试连接"验证数据库连接
3. 开始迁移：点击"开始迁移"执行数据库迁移

支持的数据库类型：
- MySQL
- PostgreSQL  
- SQL Server
- Oracle

注意事项：
- 迁移前请备份目标数据库
- 确保网络连接稳定
- 建议在非高峰时段进行迁移
        """
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
RDS数据库迁移工具 v1.0

一个功能强大的数据库迁移工具，支持：
- 传统数据库到云RDS的迁移
- RDS之间的迁移
- 不同数据库引擎间的迁移

开发团队：RDS迁移工具开发组
版本：1.0.0
        """
        messagebox.showinfo("关于", about_text)
    
    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.root.destroy()
    
    def run(self):
        """运行主窗口"""
        self.root.mainloop()
