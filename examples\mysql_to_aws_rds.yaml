# Example: Migrate from traditional MySQL to AWS RDS MySQL
# This configuration migrates a traditional MySQL database to AWS RDS

source:
  host: "on-premise-mysql.company.com"
  port: 3306
  database: "production_db"
  username: "mysql_user"
  password: "mysql_password"
  db_type: "mysql"
  charset: "utf8mb4"

target:
  host: "myapp-db.cluster-xyz.us-east-1.rds.amazonaws.com"
  port: 3306
  database: "production_db"
  username: "admin"
  password: "rds_password"
  db_type: "mysql"
  charset: "utf8mb4"

migration:
  # Migrate specific tables only
  tables:
    - "users"
    - "orders"
    - "products"
    - "categories"
    - "order_items"
  
  # Exclude temporary and log tables
  exclude_tables:
    - "temp_data"
    - "access_logs"
    - "error_logs"
    - "session_data"
  
  # Performance settings
  batch_size: 2000
  parallel_workers: 6
  
  # Schema options
  create_indexes: true
  create_foreign_keys: true
  
  # Migration type (false = migrate both schema and data)
  data_only: false
  schema_only: false
