#!/usr/bin/env python3
"""
RDS Migration Tool - Main Entry Point

A comprehensive tool for migrating databases between:
- Traditional databases to cloud RDS
- RDS to RDS (same or different providers)  
- RDS to traditional databases

Usage:
    python main.py --help
    python main.py init
    python main.py migrate -c config.yaml
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from rds_migrate.cli import cli

if __name__ == '__main__':
    cli()
