# RDS 数据库迁移工具 - API 文档

## 📋 概述

本文档详细描述了RDS数据库迁移工具的Python API接口，包括所有核心类和方法的使用说明。

## 🏗️ 核心API

### 1. 配置管理 (ConfigManager)

#### 类定义
```python
from rds_migrate.config import ConfigManager, MigrationConfig, DatabaseConfig
```

#### ConfigManager 类

##### 初始化
```python
config_manager = ConfigManager()
```

##### 主要方法

###### load_config(config_path: str) -> MigrationConfig
加载配置文件并返回配置对象。

**参数**:
- `config_path`: 配置文件路径 (支持 .yaml 和 .json)

**返回**: `MigrationConfig` 对象

**示例**:
```python
config = config_manager.load_config('migration_config.yaml')
```

###### save_config(config: MigrationConfig, config_path: str) -> None
保存配置对象到文件。

**参数**:
- `config`: MigrationConfig 对象
- `config_path`: 保存路径

**示例**:
```python
config_manager.save_config(config, 'new_config.yaml')
```

###### validate_config(config: MigrationConfig) -> bool
验证配置的有效性。

**参数**:
- `config`: 要验证的配置对象

**返回**: 验证结果 (True/False)

#### MigrationConfig 类

##### 属性
```python
@dataclass
class MigrationConfig:
    source: DatabaseConfig          # 源数据库配置
    target: DatabaseConfig          # 目标数据库配置
    tables: Optional[List[str]]     # 要迁移的表列表
    exclude_tables: Optional[List[str]]  # 排除的表列表
    batch_size: int = 1000          # 批次大小
    parallel_workers: int = 4       # 并行工作线程数
    create_indexes: bool = True     # 是否创建索引
    create_foreign_keys: bool = True # 是否创建外键
    data_only: bool = False         # 仅迁移数据
    schema_only: bool = False       # 仅迁移结构
```

##### 类方法

###### from_dict(data: Dict[str, Any]) -> MigrationConfig
从字典创建配置对象。

**参数**:
- `data`: 包含配置信息的字典

**返回**: MigrationConfig 对象

**示例**:
```python
config_dict = {
    "source": {
        "host": "localhost",
        "port": 3306,
        "username": "user",
        "password": "pass",
        "database": "source_db",
        "db_type": "mysql"
    },
    "target": {
        "host": "localhost", 
        "port": 5432,
        "username": "user",
        "password": "pass",
        "database": "target_db",
        "db_type": "postgresql"
    },
    "batch_size": 1000,
    "parallel_workers": 4
}

config = MigrationConfig.from_dict(config_dict)
```

#### DatabaseConfig 类

##### 属性
```python
@dataclass
class DatabaseConfig:
    host: str                       # 数据库主机
    port: int                       # 端口号
    username: str                   # 用户名
    password: str                   # 密码
    database: str                   # 数据库名
    db_type: str                    # 数据库类型
    charset: str = 'utf8mb4'        # 字符集
    ssl_mode: Optional[str] = None  # SSL模式
```

### 2. 数据库管理 (DatabaseManager)

#### 类定义
```python
from rds_migrate.database import DatabaseManager
```

#### 初始化
```python
from rds_migrate.config import DatabaseConfig

db_config = DatabaseConfig(
    host='localhost',
    port=3306,
    username='user',
    password='pass',
    database='test_db',
    db_type='mysql'
)

db_manager = DatabaseManager(db_config)
```

#### 主要方法

##### connect() -> Engine
建立数据库连接。

**返回**: SQLAlchemy Engine 对象

**示例**:
```python
engine = db_manager.connect()
```

##### disconnect() -> None
断开数据库连接。

**示例**:
```python
db_manager.disconnect()
```

##### test_connection() -> bool
测试数据库连接。

**返回**: 连接测试结果

**示例**:
```python
is_connected = db_manager.test_connection()
```

##### get_database_info() -> Dict[str, Any]
获取数据库信息。

**返回**: 包含数据库信息的字典

**示例**:
```python
info = db_manager.get_database_info()
print(f"数据库版本: {info['version']}")
print(f"字符集: {info['charset']}")
```

##### get_table_list() -> List[str]
获取数据库中的表列表。

**返回**: 表名列表

**示例**:
```python
tables = db_manager.get_table_list()
```

##### get_table_schema(table_name: str) -> Dict[str, Any]
获取表结构信息。

**参数**:
- `table_name`: 表名

**返回**: 表结构字典

**示例**:
```python
schema = db_manager.get_table_schema('users')
```

##### get_row_count(table_name: str) -> int
获取表的行数。

**参数**:
- `table_name`: 表名

**返回**: 行数

**示例**:
```python
count = db_manager.get_row_count('users')
```

##### execute_query(query: str, params: Optional[Dict] = None) -> Any
执行SQL查询。

**参数**:
- `query`: SQL查询语句
- `params`: 查询参数 (可选)

**返回**: 查询结果

**示例**:
```python
result = db_manager.execute_query(
    "SELECT * FROM users WHERE id = :user_id",
    {"user_id": 123}
)
```

### 3. 迁移引擎 (MigrationEngine)

#### 类定义
```python
from rds_migrate.migrator import MigrationEngine
```

#### 初始化
```python
from rds_migrate.config import MigrationConfig

engine = MigrationEngine(config)
```

#### 主要方法

##### connect_databases() -> bool
连接源和目标数据库。

**返回**: 连接结果

**示例**:
```python
success = engine.connect_databases()
```

##### disconnect_databases() -> None
断开数据库连接。

**示例**:
```python
engine.disconnect_databases()
```

##### get_tables_to_migrate() -> List[str]
获取要迁移的表列表。

**返回**: 表名列表

**示例**:
```python
tables = engine.get_tables_to_migrate()
```

##### migrate() -> MigrationStats
执行完整的数据库迁移。

**返回**: 迁移统计信息

**示例**:
```python
stats = engine.migrate()
print(f"迁移了 {stats.tables_migrated} 个表")
print(f"迁移了 {stats.rows_migrated} 行数据")
```

##### migrate_table(table_name: str) -> bool
迁移单个表。

**参数**:
- `table_name`: 要迁移的表名

**返回**: 迁移结果

**示例**:
```python
success = engine.migrate_table('users')
```

##### set_progress_callback(callback: Callable) -> None
设置进度回调函数。

**参数**:
- `callback`: 进度回调函数

**示例**:
```python
def progress_callback(table_name, current, total):
    print(f"迁移 {table_name}: {current}/{total}")

engine.set_progress_callback(progress_callback)
```

#### MigrationStats 类

##### 属性
```python
@dataclass
class MigrationStats:
    tables_migrated: int = 0        # 已迁移表数
    rows_migrated: int = 0          # 已迁移行数
    errors: int = 0                 # 错误数
    warnings: int = 0               # 警告数
    start_time: float = 0           # 开始时间
    end_time: float = 0             # 结束时间
    failed_tables: List[str] = field(default_factory=list)  # 失败的表
    performance_metrics: Dict[str, Any] = field(default_factory=dict)  # 性能指标
```

## 🔧 使用示例

### 完整迁移示例

```python
from rds_migrate.config import ConfigManager
from rds_migrate.migrator import MigrationEngine

# 1. 加载配置
config_manager = ConfigManager()
config = config_manager.load_config('migration_config.yaml')

# 2. 创建迁移引擎
engine = MigrationEngine(config)

# 3. 设置进度回调
def progress_callback(table_name, current, total):
    percentage = (current / total) * 100
    print(f"迁移 {table_name}: {percentage:.1f}% ({current}/{total})")

engine.set_progress_callback(progress_callback)

# 4. 执行迁移
try:
    # 连接数据库
    if engine.connect_databases():
        print("数据库连接成功")
        
        # 获取要迁移的表
        tables = engine.get_tables_to_migrate()
        print(f"将迁移 {len(tables)} 个表: {tables}")
        
        # 执行迁移
        stats = engine.migrate()
        
        # 显示结果
        print(f"\n迁移完成!")
        print(f"成功迁移 {stats.tables_migrated} 个表")
        print(f"迁移了 {stats.rows_migrated} 行数据")
        print(f"耗时: {stats.end_time - stats.start_time:.2f} 秒")
        
        if stats.failed_tables:
            print(f"失败的表: {stats.failed_tables}")
            
    else:
        print("数据库连接失败")
        
finally:
    # 清理连接
    engine.disconnect_databases()
```

### 单表迁移示例

```python
from rds_migrate.config import ConfigManager
from rds_migrate.migrator import MigrationEngine

# 加载配置
config_manager = ConfigManager()
config = config_manager.load_config('migration_config.yaml')

# 创建迁移引擎
engine = MigrationEngine(config)

try:
    # 连接数据库
    engine.connect_databases()
    
    # 迁移单个表
    table_name = 'users'
    success = engine.migrate_table(table_name)
    
    if success:
        print(f"表 {table_name} 迁移成功")
    else:
        print(f"表 {table_name} 迁移失败")
        
finally:
    engine.disconnect_databases()
```

### 配置验证示例

```python
from rds_migrate.config import ConfigManager

config_manager = ConfigManager()

try:
    # 加载配置
    config = config_manager.load_config('migration_config.yaml')
    
    # 验证配置
    if config_manager.validate_config(config):
        print("配置验证通过")
    else:
        print("配置验证失败")
        
except Exception as e:
    print(f"配置加载失败: {e}")
```

## 🚨 异常处理

### 常见异常类型

#### DatabaseConnectionError
数据库连接错误。

```python
from rds_migrate.database import DatabaseConnectionError

try:
    db_manager.connect()
except DatabaseConnectionError as e:
    print(f"数据库连接失败: {e}")
```

#### ConfigurationError
配置错误。

```python
from rds_migrate.config import ConfigurationError

try:
    config = config_manager.load_config('invalid_config.yaml')
except ConfigurationError as e:
    print(f"配置错误: {e}")
```

#### MigrationError
迁移过程错误。

```python
from rds_migrate.migrator import MigrationError

try:
    stats = engine.migrate()
except MigrationError as e:
    print(f"迁移失败: {e}")
```

---

*本文档最后更新: 2025-01-26*
