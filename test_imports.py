#!/usr/bin/env python3
"""
测试所有导入是否正常工作
"""

import sys
print("Python版本:", sys.version)
print("Python路径:", sys.executable)
print("当前工作目录:", sys.path[0])

print("\n=== 测试基础模块导入 ===")
try:
    import click
    print("✅ click导入成功")
except ImportError as e:
    print("❌ click导入失败:", e)

try:
    import yaml
    print("✅ yaml导入成功")
except ImportError as e:
    print("❌ yaml导入失败:", e)

try:
    import tabulate
    print("✅ tabulate导入成功")
except ImportError as e:
    print("❌ tabulate导入失败:", e)

print("\n=== 测试数据库驱动导入 ===")
try:
    import sqlalchemy
    print("✅ sqlalchemy导入成功，版本:", sqlalchemy.__version__)
except ImportError as e:
    print("❌ sqlalchemy导入失败:", e)

try:
    import pymysql
    print("✅ pymysql导入成功")
except ImportError as e:
    print("❌ pymysql导入失败:", e)

try:
    import psycopg2
    print("✅ psycopg2导入成功")
except ImportError as e:
    print("❌ psycopg2导入失败:", e)

try:
    import pyodbc
    print("✅ pyodbc导入成功")
except ImportError as e:
    print("❌ pyodbc导入失败:", e)

try:
    import tqdm
    print("✅ tqdm导入成功")
except ImportError as e:
    print("❌ tqdm导入失败:", e)

print("\n=== 测试项目模块导入 ===")
try:
    from rds_migrate.config import ConfigManager
    print("✅ config模块导入成功")
except ImportError as e:
    print("❌ config模块导入失败:", e)

try:
    from rds_migrate.database import DatabaseManager
    print("✅ database模块导入成功")
except ImportError as e:
    print("❌ database模块导入失败:", e)

try:
    from rds_migrate.migrator import MigrationEngine
    print("✅ migrator模块导入成功")
except ImportError as e:
    print("❌ migrator模块导入失败:", e)

try:
    from rds_migrate.cli import main as cli_main
    print("✅ CLI模块导入成功")
except ImportError as e:
    print("❌ CLI模块导入失败:", e)

try:
    from rds_migrate.gui.main_window import main as gui_main
    print("✅ GUI模块导入成功")
except ImportError as e:
    print("❌ GUI模块导入失败:", e)

print("\n=== 测试GUI相关模块 ===")
try:
    import tkinter
    print("✅ tkinter导入成功")
except ImportError as e:
    print("❌ tkinter导入失败:", e)

try:
    import tkinter.ttk
    print("✅ tkinter.ttk导入成功")
except ImportError as e:
    print("❌ tkinter.ttk导入失败:", e)

print("\n=== 测试完成 ===")
