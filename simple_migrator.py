#!/usr/bin/env python3
"""
简化版RDS迁移工具 - 不依赖外部包的基本版本
"""

import sys
import os
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migration.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class SimpleMigrator:
    """简化版迁移器"""
    
    def __init__(self):
        self.config = None
        
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.endswith('.json'):
                    self.config = json.load(f)
                else:
                    logger.error("目前只支持JSON配置文件")
                    return False
            logger.info(f"配置文件加载成功: {config_file}")
            return True
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return False
    
    def create_sample_config(self, output_file="sample_config.json"):
        """创建示例配置文件"""
        sample_config = {
            "source": {
                "db_type": "mysql",
                "host": "localhost",
                "port": 3306,
                "database": "source_db",
                "username": "user",
                "password": "password"
            },
            "target": {
                "db_type": "mysql", 
                "host": "localhost",
                "port": 3306,
                "database": "target_db",
                "username": "user",
                "password": "password"
            },
            "migration": {
                "batch_size": 1000,
                "parallel_workers": 4,
                "tables": ["table1", "table2"],
                "exclude_tables": [],
                "data_only": False,
                "schema_only": False
            }
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(sample_config, f, indent=2, ensure_ascii=False)
            logger.info(f"示例配置文件已创建: {output_file}")
            return True
        except Exception as e:
            logger.error(f"创建示例配置文件失败: {e}")
            return False
    
    def test_connection(self, db_config):
        """测试数据库连接"""
        try:
            from rds_migrate.database import DatabaseManager
            db_manager = DatabaseManager(db_config)
            if db_manager.test_connection():
                logger.info(f"数据库连接测试成功: {db_config.get('host', 'unknown')}")
                return True
            else:
                logger.error(f"数据库连接测试失败: {db_config.get('host', 'unknown')}")
                return False
        except Exception as e:
            logger.error(f"连接测试出错: {e}")
            return False
    
    def run_migration(self):
        """运行迁移"""
        if not self.config:
            logger.error("请先加载配置文件")
            return False
        
        try:
            from rds_migrate.migrator import MigrationEngine
            from rds_migrate.config import MigrationConfig
            
            # 创建迁移配置
            migration_config = MigrationConfig.from_dict(self.config)
            
            # 创建迁移引擎
            engine = MigrationEngine(migration_config)
            
            # 运行迁移
            logger.info("开始数据库迁移...")
            success = engine.migrate()
            
            if success:
                logger.info("数据库迁移完成！")
                return True
            else:
                logger.error("数据库迁移失败")
                return False
                
        except Exception as e:
            logger.error(f"迁移过程出错: {e}")
            return False


def show_help():
    """显示帮助信息"""
    help_text = """
简化版RDS迁移工具使用说明:

命令:
  python simple_migrator.py init [文件名]     - 创建示例配置文件
  python simple_migrator.py test <配置文件>   - 测试数据库连接
  python simple_migrator.py migrate <配置文件> - 执行数据库迁移
  python simple_migrator.py help             - 显示此帮助信息

示例:
  python simple_migrator.py init my_config.json
  python simple_migrator.py test my_config.json
  python simple_migrator.py migrate my_config.json

配置文件格式: JSON
支持的数据库: MySQL, PostgreSQL, SQL Server
    """
    print(help_text)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    migrator = SimpleMigrator()
    
    if command == "init":
        output_file = sys.argv[2] if len(sys.argv) > 2 else "sample_config.json"
        migrator.create_sample_config(output_file)
        
    elif command == "test":
        if len(sys.argv) < 3:
            logger.error("请指定配置文件")
            return
        
        config_file = sys.argv[2]
        if migrator.load_config(config_file):
            # 测试源数据库连接
            logger.info("测试源数据库连接...")
            migrator.test_connection(migrator.config['source'])
            
            # 测试目标数据库连接
            logger.info("测试目标数据库连接...")
            migrator.test_connection(migrator.config['target'])
            
    elif command == "migrate":
        if len(sys.argv) < 3:
            logger.error("请指定配置文件")
            return
        
        config_file = sys.argv[2]
        if migrator.load_config(config_file):
            migrator.run_migration()
            
    elif command == "help":
        show_help()
        
    else:
        logger.error(f"未知命令: {command}")
        show_help()


if __name__ == "__main__":
    main()
