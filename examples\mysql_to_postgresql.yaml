# Example: Cross-database migration from MySQL to PostgreSQL
# This configuration demonstrates migrating between different database engines

source:
  host: "mysql-server.company.com"
  port: 3306
  database: "legacy_db"
  username: "mysql_user"
  password: "mysql_password"
  db_type: "mysql"
  charset: "utf8mb4"

target:
  host: "postgres-rds.amazonaws.com"
  port: 5432
  database: "modern_db"
  username: "postgres_user"
  password: "postgres_password"
  db_type: "postgresql"
  ssl_mode: "prefer"

migration:
  # Migrate core business tables
  tables:
    - "customers"
    - "orders"
    - "products"
    - "inventory"
    - "payments"
  
  # Exclude MySQL-specific tables
  exclude_tables:
    - "mysql_logs"
    - "performance_schema"
    - "information_schema"
  
  # Smaller batches for cross-database migration
  batch_size: 500
  parallel_workers: 2
  
  # Create indexes but be careful with foreign keys across engines
  create_indexes: true
  create_foreign_keys: false  # May need manual adjustment
  
  data_only: false
  schema_only: false
