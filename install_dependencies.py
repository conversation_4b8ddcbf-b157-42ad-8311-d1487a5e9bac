#!/usr/bin/env python3
"""
依赖包安装脚本 - 使用国内镜像源
"""

import sys
import subprocess
import os

def install_with_tsinghua_mirror():
    """使用清华大学镜像安装依赖"""
    print("🚀 使用清华大学镜像安装依赖包...")
    
    # 基础依赖包
    basic_packages = [
        'click==8.1.7',
        'pyyaml==6.0.1', 
        'tabulate==0.9.0',
        'tqdm==4.67.1',
        'sqlalchemy==2.0.41',
        'pymysql==1.1.1',
        'psycopg2-binary==2.9.10',
        'pyodbc==5.2.0'
    ]
    
    try:
        cmd = [
            sys.executable, '-m', 'pip', 'install',
            '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple/',
            '--trusted-host', 'pypi.tuna.tsinghua.edu.cn'
        ] + basic_packages
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True)
        
        print("✅ 基础依赖包安装成功！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def install_with_aliyun_mirror():
    """使用阿里云镜像安装依赖"""
    print("🚀 使用阿里云镜像安装依赖包...")
    
    basic_packages = [
        'click==8.1.7',
        'pyyaml==6.0.1', 
        'tabulate==0.9.0',
        'tqdm==4.67.1',
        'sqlalchemy==2.0.41',
        'pymysql==1.1.1',
        'psycopg2-binary==2.9.10',
        'pyodbc==5.2.0'
    ]
    
    try:
        cmd = [
            sys.executable, '-m', 'pip', 'install',
            '-i', 'https://mirrors.aliyun.com/pypi/simple/',
            '--trusted-host', 'mirrors.aliyun.com'
        ] + basic_packages
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True)
        
        print("✅ 基础依赖包安装成功！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def install_with_douban_mirror():
    """使用豆瓣镜像安装依赖"""
    print("🚀 使用豆瓣镜像安装依赖包...")
    
    basic_packages = [
        'click==8.1.7',
        'pyyaml==6.0.1', 
        'tabulate==0.9.0',
        'tqdm==4.67.1',
        'sqlalchemy==2.0.41',
        'pymysql==1.1.1',
        'psycopg2-binary==2.9.10',
        'pyodbc==5.2.0'
    ]
    
    try:
        cmd = [
            sys.executable, '-m', 'pip', 'install',
            '-i', 'https://pypi.douban.com/simple/',
            '--trusted-host', 'pypi.douban.com'
        ] + basic_packages
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True)
        
        print("✅ 基础依赖包安装成功！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def test_imports():
    """测试导入是否成功"""
    print("\n🧪 测试依赖包导入...")
    
    packages = [
        ('click', 'click'),
        ('yaml', 'pyyaml'),
        ('tabulate', 'tabulate'),
        ('tqdm', 'tqdm'),
        ('sqlalchemy', 'sqlalchemy'),
        ('pymysql', 'pymysql'),
        ('psycopg2', 'psycopg2-binary'),
        ('pyodbc', 'pyodbc')
    ]
    
    success_count = 0
    for module_name, package_name in packages:
        try:
            __import__(module_name)
            print(f"✅ {package_name} 导入成功")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name} 导入失败")
    
    print(f"\n📊 导入测试结果: {success_count}/{len(packages)} 成功")
    return success_count == len(packages)

def main():
    """主函数"""
    print("=" * 60)
    print("📦 RDS迁移工具依赖包安装脚本")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 尝试不同的镜像源
    mirrors = [
        ("清华大学镜像", install_with_tsinghua_mirror),
        ("阿里云镜像", install_with_aliyun_mirror),
        ("豆瓣镜像", install_with_douban_mirror)
    ]
    
    for mirror_name, install_func in mirrors:
        print(f"\n🔄 尝试使用{mirror_name}...")
        if install_func():
            print(f"✅ 使用{mirror_name}安装成功！")
            break
        else:
            print(f"❌ {mirror_name}安装失败，尝试下一个镜像...")
    else:
        print("❌ 所有镜像都安装失败，请检查网络连接或手动安装")
        return False
    
    # 测试导入
    if test_imports():
        print("\n🎉 所有依赖包安装并测试成功！")
        print("现在可以运行: python start_rds_migrate.py")
        return True
    else:
        print("\n❌ 部分依赖包导入失败，请检查安装")
        return False

if __name__ == '__main__':
    main()
