# Example: Migrate from one PostgreSQL instance to another
# This configuration migrates between different PostgreSQL providers

source:
  host: "source-postgres.herokuapp.com"
  port: 5432
  database: "app_db"
  username: "postgres_user"
  password: "source_password"
  db_type: "postgresql"
  ssl_mode: "require"

target:
  host: "target-postgres.azure.com"
  port: 5432
  database: "app_db"
  username: "postgres_admin"
  password: "target_password"
  db_type: "postgresql"
  ssl_mode: "require"

migration:
  # Migrate all tables (no specific table list)
  tables: null
  
  # Exclude system and temporary tables
  exclude_tables:
    - "pg_stat_statements"
    - "temp_analytics"
    - "cache_data"
  
  # Conservative settings for cross-provider migration
  batch_size: 1000
  parallel_workers: 3
  
  # Full migration with indexes and constraints
  create_indexes: true
  create_foreign_keys: true
  
  data_only: false
  schema_only: false
