"""
高级连接池管理器 - 智能连接池，支持连接复用、健康检查、自动重连和负载均衡
优化版本包含增强的类型安全、错误处理和性能监控
"""

import asyncio
import time
import logging
import gc
import psutil
from typing import Dict, List, Optional, Any, Union, Callable, Protocol, TypeVar, Generic
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from enum import Enum, auto
import weakref
import threading
from concurrent.futures import ThreadPoolExecutor
from abc import ABC, abstractmethod

# 数据库驱动导入（带错误处理）
try:
    import aiomysql
except ImportError:
    aiomysql = None

try:
    import asyncpg
except ImportError:
    asyncpg = None

try:
    import pymysql
except ImportError:
    pymysql = None

try:
    import psycopg2
    from psycopg2 import pool as psycopg2_pool
except ImportError:
    psycopg2 = None
    psycopg2_pool = None

from .config import DatabaseConfig

logger = logging.getLogger(__name__)

# 类型变量
T = TypeVar('T')


class ConnectionState(Enum):
    """连接状态枚举，增强版本"""
    IDLE = "idle"
    ACTIVE = "active"
    UNHEALTHY = "unhealthy"
    CLOSED = "closed"
    CONNECTING = "connecting"
    RECONNECTING = "reconnecting"
    ERROR = "error"


class PoolState(Enum):
    """连接池状态枚举"""
    INITIALIZING = auto()
    RUNNING = auto()
    DEGRADED = auto()
    CLOSING = auto()
    CLOSED = auto()
    ERROR = auto()


@dataclass
class ConnectionMetrics:
    """
    连接指标，增强版本包含更详细的性能统计
    """
    created_at: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)
    last_health_check: float = field(default_factory=time.time)
    total_queries: int = 0
    failed_queries: int = 0
    successful_queries: int = 0
    avg_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    total_response_time: float = 0.0
    state: ConnectionState = ConnectionState.IDLE
    error_count: int = 0
    last_error: Optional[str] = None
    reconnect_count: int = 0

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_queries == 0:
            return 100.0
        return (self.successful_queries / self.total_queries) * 100.0

    @property
    def age_seconds(self) -> float:
        """连接年龄（秒）"""
        return time.time() - self.created_at

    @property
    def idle_time(self) -> float:
        """空闲时间（秒）"""
        return time.time() - self.last_used

    def update_response_time(self, response_time: float) -> None:
        """更新响应时间统计"""
        self.total_response_time += response_time
        self.min_response_time = min(self.min_response_time, response_time)
        self.max_response_time = max(self.max_response_time, response_time)
        if self.total_queries > 0:
            self.avg_response_time = self.total_response_time / self.total_queries


@dataclass
class PoolConfig:
    """
    连接池配置，增强版本包含更多优化选项
    """
    min_size: int = 2
    max_size: int = 10
    max_idle_time: int = 300  # 5分钟
    connection_timeout: int = 30  # 连接超时
    health_check_interval: int = 60  # 健康检查间隔
    max_retries: int = 3  # 最大重试次数
    retry_delay: float = 1.0  # 重试延迟
    enable_monitoring: bool = True  # 启用监控
    max_memory_usage: int = 512 * 1024 * 1024  # 最大内存使用（512MB）
    enable_query_logging: bool = False  # 启用查询日志
    pool_recycle_time: int = 3600  # 连接回收时间（1小时）

    def __post_init__(self):
        """验证配置参数"""
        if self.min_size < 0:
            raise ValueError("min_size must be >= 0")
        if self.max_size < self.min_size:
            raise ValueError("max_size must be >= min_size")
        if self.connection_timeout <= 0:
            raise ValueError("connection_timeout must be > 0")
        if self.health_check_interval <= 0:
            raise ValueError("health_check_interval must be > 0")


class ConnectionProtocol(Protocol):
    """连接协议定义"""
    async def execute(self, query: str, *args) -> Any: ...
    async def close(self) -> None: ...


class PoolProtocol(Protocol, Generic[T]):
    """连接池协议定义"""
    async def acquire(self) -> T: ...
    async def release(self, connection: T) -> None: ...
    async def close(self) -> None: ...
    health_check_interval: int = 60  # 1分钟
    connection_timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    enable_load_balancing: bool = True


class AsyncConnectionWrapper:
    """异步连接包装器，增强版本包含完整的类型安全和错误处理"""

    def __init__(self, connection: Any, config: DatabaseConfig, pool: 'AsyncConnectionPool') -> None:
        self.connection: Any = connection
        self.config: DatabaseConfig = config
        self.pool: 'AsyncConnectionPool' = pool
        self.metrics: ConnectionMetrics = ConnectionMetrics()
        self._lock: asyncio.Lock = asyncio.Lock()
        self._last_activity: float = time.time()
        self._query_count: int = 0
        self._error_count: int = 0
        
    async def execute(self, query: str, params: Optional[tuple] = None) -> Any:
        """执行查询，增强版本包含完整的错误处理和性能监控"""
        start_time = time.time()
        try:
            async with self._lock:
                self._last_activity = time.time()
                self.metrics.state = ConnectionState.ACTIVE
                self.metrics.last_used = self._last_activity
                self._query_count += 1

                result: Any = None

                # 数据库特定的查询执行
                if self.config.db_type == 'mysql':
                    if not aiomysql:
                        raise RuntimeError("aiomysql 模块不可用")
                    async with self.connection.cursor() as cursor:
                        await cursor.execute(query, params)
                        result = await cursor.fetchall()
                elif self.config.db_type == 'postgresql':
                    if not asyncpg:
                        raise RuntimeError("asyncpg 模块不可用")
                    result = await self.connection.fetch(query, *(params or ()))
                else:
                    raise ValueError(f"不支持的数据库类型: {self.config.db_type}")

                # 更新性能指标
                response_time = time.time() - start_time
                self.metrics.total_queries += 1
                self.metrics.update_response_time(response_time)
                self.metrics.state = ConnectionState.IDLE

                logger.debug(f"查询执行成功，耗时: {response_time:.3f}s")
                return result

        except Exception as e:
            self._error_count += 1
            self.metrics.failed_queries += 1
            self.metrics.state = ConnectionState.UNHEALTHY
            logger.error(f"查询执行失败: {str(e)}, 查询: {query[:100]}...")
            raise
    
    async def health_check(self) -> bool:
        """健康检查，增强版本包含更详细的检查和错误处理"""
        try:
            # 检查连接年龄
            if self.metrics.age_seconds > self.pool.pool_config.pool_recycle_time:
                logger.info(f"连接已超过回收时间 ({self.metrics.age_seconds}s)，标记为不健康")
                self.metrics.state = ConnectionState.UNHEALTHY
                return False

            # 检查空闲时间
            if self.metrics.idle_time > self.pool.pool_config.max_idle_time:
                logger.debug(f"连接空闲时间过长 ({self.metrics.idle_time}s)")

            # 执行简单的健康检查查询
            start_time = time.time()

            if self.config.db_type == 'mysql':
                await self.execute("SELECT 1 as health_check")
            elif self.config.db_type == 'postgresql':
                await self.execute("SELECT 1 as health_check")
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")

            # 更新健康检查指标
            check_time = time.time() - start_time
            if check_time > 1.0:  # 健康检查超过1秒认为性能有问题
                logger.warning(f"健康检查耗时过长: {check_time:.3f}s")

            if self.metrics.state == ConnectionState.UNHEALTHY:
                self.metrics.state = ConnectionState.IDLE

            logger.debug(f"连接健康检查通过，耗时: {check_time:.3f}s")
            return True

        except Exception as e:
            self._error_count += 1
            logger.warning(f"连接健康检查失败: {str(e)}")
            self.metrics.state = ConnectionState.UNHEALTHY
            return False
    
    async def close(self) -> None:
        """关闭连接，增强版本包含完整的资源清理"""
        try:
            async with self._lock:
                if self.metrics.state == ConnectionState.CLOSED:
                    logger.debug("连接已经关闭")
                    return

                logger.debug(f"关闭连接，总查询数: {self._query_count}, 错误数: {self._error_count}")

                # 数据库特定的关闭操作
                if self.config.db_type == 'mysql':
                    if hasattr(self.connection, 'ensure_closed'):
                        await self.connection.ensure_closed()
                    elif hasattr(self.connection, 'close'):
                        await self.connection.close()
                elif self.config.db_type == 'postgresql':
                    if hasattr(self.connection, 'close'):
                        await self.connection.close()

                self.metrics.state = ConnectionState.CLOSED
                logger.debug("连接已成功关闭")

        except Exception as e:
            logger.error(f"关闭连接失败: {str(e)}")
            self.metrics.state = ConnectionState.ERROR
        finally:
            # 确保状态被设置
            if self.metrics.state not in [ConnectionState.CLOSED, ConnectionState.ERROR]:
                self.metrics.state = ConnectionState.CLOSED


class AsyncConnectionPool:
    """异步连接池"""
    
    def __init__(self, config: DatabaseConfig, pool_config: PoolConfig = None):
        self.config = config
        self.pool_config = pool_config or PoolConfig()
        self.connections: List[AsyncConnectionWrapper] = []
        self.available_connections: asyncio.Queue = asyncio.Queue()
        self.total_connections = 0
        self._lock = asyncio.Lock()
        self._health_check_task: Optional[asyncio.Task] = None
        self._metrics = {
            'total_requests': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'avg_wait_time': 0.0
        }
    
    async def initialize(self):
        """初始化连接池"""
        logger.info(f"初始化连接池，最小连接数: {self.pool_config.min_size}")
        
        # 创建最小连接数
        for _ in range(self.pool_config.min_size):
            await self._create_connection()
        
        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info(f"连接池初始化完成，当前连接数: {len(self.connections)}")
    
    async def _create_connection(self) -> AsyncConnectionWrapper:
        """创建新连接"""
        try:
            if self.config.db_type == 'mysql':
                connection = await aiomysql.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    db=self.config.database,
                    charset=self.config.charset or 'utf8mb4',
                    autocommit=True
                )
            elif self.config.db_type == 'postgresql':
                connection = await asyncpg.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database
                )
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")
            
            wrapper = AsyncConnectionWrapper(connection, self.config, self)
            self.connections.append(wrapper)
            await self.available_connections.put(wrapper)
            self.total_connections += 1
            
            logger.debug(f"创建新连接成功，总连接数: {self.total_connections}")
            return wrapper
            
        except Exception as e:
            self._metrics['failed_connections'] += 1
            logger.error(f"创建连接失败: {str(e)}")
            raise
    
    @asynccontextmanager
    async def get_connection(self):
        """获取连接（上下文管理器）"""
        start_time = time.time()
        connection = None
        
        try:
            # 尝试获取可用连接
            try:
                connection = await asyncio.wait_for(
                    self.available_connections.get(),
                    timeout=self.pool_config.connection_timeout
                )
            except asyncio.TimeoutError:
                # 如果没有可用连接且未达到最大连接数，创建新连接
                if self.total_connections < self.pool_config.max_size:
                    connection = await self._create_connection()
                else:
                    raise Exception("连接池已满，无法获取连接")
            
            # 检查连接健康状态
            if connection.metrics.state == ConnectionState.UNHEALTHY:
                if not await connection.health_check():
                    # 连接不健康，重新创建
                    await self._replace_connection(connection)
                    connection = await self._create_connection()
            
            # 更新指标
            wait_time = time.time() - start_time
            self._metrics['total_requests'] += 1
            self._metrics['active_connections'] += 1
            self._metrics['avg_wait_time'] = (
                (self._metrics['avg_wait_time'] * (self._metrics['total_requests'] - 1) + wait_time)
                / self._metrics['total_requests']
            )
            
            yield connection
            
        finally:
            if connection:
                # 归还连接到池中
                self._metrics['active_connections'] -= 1
                if connection.metrics.state != ConnectionState.UNHEALTHY:
                    await self.available_connections.put(connection)
    
    async def _replace_connection(self, old_connection: AsyncConnectionWrapper):
        """替换不健康的连接"""
        try:
            await old_connection.close()
            if old_connection in self.connections:
                self.connections.remove(old_connection)
            self.total_connections -= 1
            logger.debug(f"移除不健康连接，剩余连接数: {self.total_connections}")
        except Exception as e:
            logger.error(f"移除连接失败: {str(e)}")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.pool_config.health_check_interval)
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查循环出错: {str(e)}")
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        unhealthy_connections = []
        
        for connection in self.connections:
            if connection.metrics.state != ConnectionState.ACTIVE:
                if not await connection.health_check():
                    unhealthy_connections.append(connection)
        
        # 移除不健康的连接
        for connection in unhealthy_connections:
            await self._replace_connection(connection)
        
        # 确保最小连接数
        while len(self.connections) < self.pool_config.min_size:
            try:
                await self._create_connection()
            except Exception as e:
                logger.error(f"补充连接失败: {str(e)}")
                break
    
    async def close(self):
        """关闭连接池"""
        logger.info("关闭连接池...")
        
        # 取消健康检查任务
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        for connection in self.connections:
            await connection.close()
        
        self.connections.clear()
        self.total_connections = 0
        
        logger.info("连接池已关闭")
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取连接池指标"""
        healthy_connections = sum(
            1 for conn in self.connections 
            if conn.metrics.state in [ConnectionState.IDLE, ConnectionState.ACTIVE]
        )
        
        return {
            **self._metrics,
            'total_connections': self.total_connections,
            'healthy_connections': healthy_connections,
            'unhealthy_connections': self.total_connections - healthy_connections,
            'available_connections': self.available_connections.qsize(),
            'pool_config': {
                'min_size': self.pool_config.min_size,
                'max_size': self.pool_config.max_size,
                'max_idle_time': self.pool_config.max_idle_time
            }
        }


class SyncConnectionPool:
    """同步连接池（用于传统同步代码）"""
    
    def __init__(self, config: DatabaseConfig, pool_config: PoolConfig = None):
        self.config = config
        self.pool_config = pool_config or PoolConfig()
        self.connections: List[Any] = []
        self.available_connections: List[Any] = []
        self._lock = threading.Lock()
        self._health_check_thread: Optional[threading.Thread] = None
        self._shutdown = False
    
    def initialize(self):
        """初始化连接池"""
        logger.info(f"初始化同步连接池，最小连接数: {self.pool_config.min_size}")
        
        with self._lock:
            for _ in range(self.pool_config.min_size):
                self._create_connection()
        
        # 启动健康检查线程
        self._health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self._health_check_thread.start()
        
        logger.info(f"同步连接池初始化完成，当前连接数: {len(self.connections)}")
    
    def _create_connection(self) -> Any:
        """创建新连接"""
        try:
            if self.config.db_type == 'mysql':
                connection = pymysql.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database,
                    charset=self.config.charset or 'utf8mb4',
                    autocommit=True
                )
            elif self.config.db_type == 'postgresql':
                connection = psycopg2.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database
                )
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")
            
            self.connections.append(connection)
            self.available_connections.append(connection)
            
            logger.debug(f"创建同步连接成功，总连接数: {len(self.connections)}")
            return connection
            
        except Exception as e:
            logger.error(f"创建同步连接失败: {str(e)}")
            raise
    
    def get_connection(self):
        """获取连接"""
        with self._lock:
            if self.available_connections:
                return self.available_connections.pop()
            elif len(self.connections) < self.pool_config.max_size:
                return self._create_connection()
            else:
                raise Exception("同步连接池已满")
    
    def return_connection(self, connection: Any):
        """归还连接"""
        with self._lock:
            if connection in self.connections:
                self.available_connections.append(connection)
    
    def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown:
            try:
                time.sleep(self.pool_config.health_check_interval)
                self._perform_health_checks()
            except Exception as e:
                logger.error(f"同步健康检查出错: {str(e)}")
    
    def _perform_health_checks(self):
        """执行健康检查"""
        with self._lock:
            unhealthy_connections = []
            
            for connection in self.connections:
                try:
                    cursor = connection.cursor()
                    cursor.execute("SELECT 1")
                    cursor.close()
                except Exception:
                    unhealthy_connections.append(connection)
            
            # 移除不健康的连接
            for connection in unhealthy_connections:
                try:
                    connection.close()
                    self.connections.remove(connection)
                    if connection in self.available_connections:
                        self.available_connections.remove(connection)
                except Exception as e:
                    logger.error(f"移除不健康连接失败: {str(e)}")
    
    def close(self):
        """关闭连接池"""
        logger.info("关闭同步连接池...")
        self._shutdown = True
        
        with self._lock:
            for connection in self.connections:
                try:
                    connection.close()
                except Exception as e:
                    logger.error(f"关闭连接失败: {str(e)}")
            
            self.connections.clear()
            self.available_connections.clear()
        
        logger.info("同步连接池已关闭")


class ConnectionPoolManager:
    """连接池管理器"""
    
    def __init__(self):
        self.async_pools: Dict[str, AsyncConnectionPool] = {}
        self.sync_pools: Dict[str, SyncConnectionPool] = {}
    
    async def get_async_pool(self, config: DatabaseConfig, pool_config: PoolConfig = None) -> AsyncConnectionPool:
        """获取异步连接池"""
        pool_key = f"{config.host}:{config.port}:{config.database}:{config.username}"
        
        if pool_key not in self.async_pools:
            pool = AsyncConnectionPool(config, pool_config)
            await pool.initialize()
            self.async_pools[pool_key] = pool
        
        return self.async_pools[pool_key]
    
    def get_sync_pool(self, config: DatabaseConfig, pool_config: PoolConfig = None) -> SyncConnectionPool:
        """获取同步连接池"""
        pool_key = f"{config.host}:{config.port}:{config.database}:{config.username}"
        
        if pool_key not in self.sync_pools:
            pool = SyncConnectionPool(config, pool_config)
            pool.initialize()
            self.sync_pools[pool_key] = pool
        
        return self.sync_pools[pool_key]
    
    async def close_all(self):
        """关闭所有连接池"""
        # 关闭异步连接池
        for pool in self.async_pools.values():
            await pool.close()
        self.async_pools.clear()
        
        # 关闭同步连接池
        for pool in self.sync_pools.values():
            pool.close()
        self.sync_pools.clear()


# 全局连接池管理器实例
pool_manager = ConnectionPoolManager()
