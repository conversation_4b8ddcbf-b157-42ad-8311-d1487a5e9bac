/**
 * 增强版Web界面JavaScript - 现代化交互功能
 */

class EnhancedUI {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'light';
        this.notifications = [];
        this.charts = {};
        this.websocket = null;
        this.init();
    }

    init() {
        this.initTheme();
        this.initEventListeners();
        this.initWebSocket();
        this.initCharts();
        this.initTooltips();
        this.loadDashboardData();
    }

    // 主题管理
    initTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.theme);
        localStorage.setItem('theme', this.theme);
        this.showNotification('主题已切换', 'info');
    }

    // 事件监听器
    initEventListeners() {
        // 表单提交
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('ajax-form')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });

        // 模态框
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-trigger')) {
                e.preventDefault();
                this.openModal(e.target.dataset.modal);
            }
            if (e.target.classList.contains('modal-close') || e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.openCommandPalette();
            }
        });

        // 自动保存
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('auto-save')) {
                this.debounce(() => this.autoSave(e.target), 1000)();
            }
        });
    }

    // WebSocket连接
    initWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket连接已建立');
                this.showNotification('实时连接已建立', 'success');
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.showNotification('实时连接已断开', 'warning');
                // 尝试重连
                setTimeout(() => this.initWebSocket(), 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.showNotification('连接错误', 'error');
            };
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'migration_progress':
                this.updateMigrationProgress(data.payload);
                break;
            case 'performance_metrics':
                this.updatePerformanceMetrics(data.payload);
                break;
            case 'notification':
                this.showNotification(data.payload.message, data.payload.type);
                break;
            case 'table_status':
                this.updateTableStatus(data.payload);
                break;
            default:
                console.log('未知消息类型:', data.type);
        }
    }

    // 图表初始化
    initCharts() {
        // 性能监控图表
        this.initPerformanceChart();
        // 迁移进度图表
        this.initProgressChart();
        // 错误统计图表
        this.initErrorChart();
    }

    initPerformanceChart() {
        const ctx = document.getElementById('performanceChart');
        if (!ctx) return;

        this.charts.performance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU使用率',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4
                }, {
                    label: '内存使用率',
                    data: [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    initProgressChart() {
        const ctx = document.getElementById('progressChart');
        if (!ctx) return;

        this.charts.progress = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['已完成', '进行中', '待处理'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#10b981', '#f59e0b', '#e5e7eb'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    initErrorChart() {
        const ctx = document.getElementById('errorChart');
        if (!ctx) return;

        this.charts.error = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: '错误数量',
                    data: [],
                    backgroundColor: '#ef4444',
                    borderColor: '#dc2626',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // 工具提示
    initTooltips() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(element => {
            const tooltipText = element.dataset.tooltip;
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip-text';
            tooltip.textContent = tooltipText;
            element.appendChild(tooltip);
            element.classList.add('tooltip');
        });
    }

    // 通知系统
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, duration);

        this.notifications.push(notification);
    }

    // 模态框管理
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal() {
        const activeModal = document.querySelector('.modal-overlay.active');
        if (activeModal) {
            activeModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // 表单处理
    async handleFormSubmit(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="loading-spinner"></span> 处理中...';
        
        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: form.method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message || '操作成功', 'success');
                if (result.redirect) {
                    window.location.href = result.redirect;
                }
                if (result.reload) {
                    window.location.reload();
                }
            } else {
                this.showNotification(result.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('表单提交错误:', error);
            this.showNotification('网络错误，请重试', 'error');
        } finally {
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }

    // 自动保存
    autoSave(element) {
        const data = {
            field: element.name,
            value: element.value,
            id: element.dataset.id
        };
        
        fetch('/api/auto-save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
        }).then(response => response.json())
          .then(result => {
              if (result.success) {
                  element.classList.add('saved');
                  setTimeout(() => element.classList.remove('saved'), 2000);
              }
          }).catch(error => {
              console.error('自动保存失败:', error);
          });
    }

    // 仪表板数据加载
    async loadDashboardData() {
        try {
            const response = await fetch('/api/dashboard');
            const data = await response.json();
            
            this.updateDashboardMetrics(data.metrics);
            this.updateCharts(data.charts);
        } catch (error) {
            console.error('加载仪表板数据失败:', error);
        }
    }

    updateDashboardMetrics(metrics) {
        Object.entries(metrics).forEach(([key, value]) => {
            const element = document.querySelector(`[data-metric="${key}"]`);
            if (element) {
                this.animateNumber(element, value);
            }
        });
    }

    updateCharts(chartData) {
        if (chartData.performance && this.charts.performance) {
            this.updatePerformanceChart(chartData.performance);
        }
        if (chartData.progress && this.charts.progress) {
            this.updateProgressChart(chartData.progress);
        }
        if (chartData.errors && this.charts.error) {
            this.updateErrorChart(chartData.errors);
        }
    }

    updatePerformanceChart(data) {
        const chart = this.charts.performance;
        chart.data.labels = data.labels;
        chart.data.datasets[0].data = data.cpu;
        chart.data.datasets[1].data = data.memory;
        chart.update('none');
    }

    updateProgressChart(data) {
        const chart = this.charts.progress;
        chart.data.datasets[0].data = [data.completed, data.inProgress, data.pending];
        chart.update('none');
    }

    updateErrorChart(data) {
        const chart = this.charts.error;
        chart.data.labels = data.labels;
        chart.data.datasets[0].data = data.values;
        chart.update('none');
    }

    // 数字动画
    animateNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    // 迁移进度更新
    updateMigrationProgress(data) {
        const progressBar = document.querySelector('.migration-progress-bar');
        const progressText = document.querySelector('.migration-progress-text');
        
        if (progressBar) {
            progressBar.style.width = `${data.percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${data.current}/${data.total} (${data.percentage}%)`;
        }
        
        // 更新表状态
        if (data.tables) {
            data.tables.forEach(table => {
                this.updateTableStatus(table);
            });
        }
    }

    updateTableStatus(tableData) {
        const tableRow = document.querySelector(`[data-table="${tableData.name}"]`);
        if (tableRow) {
            const statusCell = tableRow.querySelector('.table-status');
            const progressCell = tableRow.querySelector('.table-progress');
            
            if (statusCell) {
                statusCell.className = `table-status badge badge-${tableData.status}`;
                statusCell.textContent = tableData.statusText;
            }
            
            if (progressCell) {
                progressCell.textContent = `${tableData.progress}%`;
            }
        }
    }

    // 性能指标更新
    updatePerformanceMetrics(metrics) {
        // 更新CPU使用率
        const cpuElement = document.querySelector('[data-metric="cpu"]');
        if (cpuElement) {
            cpuElement.textContent = `${metrics.cpu}%`;
        }
        
        // 更新内存使用率
        const memoryElement = document.querySelector('[data-metric="memory"]');
        if (memoryElement) {
            memoryElement.textContent = `${metrics.memory}%`;
        }
        
        // 更新图表
        if (this.charts.performance) {
            const chart = this.charts.performance;
            const now = new Date().toLocaleTimeString();
            
            chart.data.labels.push(now);
            chart.data.datasets[0].data.push(metrics.cpu);
            chart.data.datasets[1].data.push(metrics.memory);
            
            // 保持最近50个数据点
            if (chart.data.labels.length > 50) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
                chart.data.datasets[1].data.shift();
            }
            
            chart.update('none');
        }
    }

    // 命令面板
    openCommandPalette() {
        // 实现命令面板功能
        console.log('打开命令面板');
    }

    // 工具函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
}

// 初始化增强UI
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedUI = new EnhancedUI();
});

// 导出给其他脚本使用
window.EnhancedUI = EnhancedUI;
