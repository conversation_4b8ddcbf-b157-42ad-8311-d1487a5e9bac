#!/usr/bin/env python3
"""
测试运行脚本
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path


def install_test_dependencies():
    """安装测试依赖"""
    print("📦 安装测试依赖...")
    
    test_packages = [
        'pytest==8.3.4',
        'pytest-cov==6.0.0',
        'pytest-mock==3.14.0',
        'pytest-asyncio==0.24.0',
        'psutil==6.1.0'  # 用于性能测试
    ]
    
    try:
        cmd = [
            sys.executable, '-m', 'pip', 'install',
            '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple/'
        ] + test_packages
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 测试依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试依赖安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def run_unit_tests(verbose=False, coverage=False):
    """运行单元测试"""
    print("🧪 运行单元测试...")
    
    cmd = [sys.executable, '-m', 'pytest', 'tests/', '-m', 'unit']
    
    if verbose:
        cmd.append('-v')
    
    if coverage:
        cmd.extend(['--cov=rds_migrate', '--cov-report=html', '--cov-report=term'])
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 单元测试通过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 单元测试失败")
        return False


def run_integration_tests(verbose=False):
    """运行集成测试"""
    print("🔗 运行集成测试...")
    
    cmd = [sys.executable, '-m', 'pytest', 'tests/', '-m', 'integration']
    
    if verbose:
        cmd.append('-v')
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 集成测试通过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 集成测试失败")
        return False


def run_performance_tests(verbose=False):
    """运行性能测试"""
    print("⚡ 运行性能测试...")
    
    cmd = [sys.executable, '-m', 'pytest', 'tests/', '-m', 'performance']
    
    if verbose:
        cmd.append('-v')
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 性能测试通过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 性能测试失败")
        return False


def run_all_tests(verbose=False, coverage=False, skip_slow=False):
    """运行所有测试"""
    print("🚀 运行所有测试...")
    
    cmd = [sys.executable, '-m', 'pytest', 'tests/']
    
    if verbose:
        cmd.append('-v')
    
    if coverage:
        cmd.extend(['--cov=rds_migrate', '--cov-report=html', '--cov-report=term'])
    
    if skip_slow:
        cmd.extend(['-m', 'not slow'])
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 所有测试通过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 部分测试失败")
        return False


def run_specific_test(test_path, verbose=False):
    """运行特定测试"""
    print(f"🎯 运行特定测试: {test_path}")
    
    cmd = [sys.executable, '-m', 'pytest', test_path]
    
    if verbose:
        cmd.append('-v')
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 测试通过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 测试失败")
        return False


def generate_test_report():
    """生成测试报告"""
    print("📊 生成测试报告...")
    
    cmd = [
        sys.executable, '-m', 'pytest', 'tests/',
        '--cov=rds_migrate',
        '--cov-report=html:htmlcov',
        '--cov-report=xml:coverage.xml',
        '--cov-report=term',
        '--junit-xml=test-results.xml'
    ]
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 测试报告生成成功")
        print("📁 HTML报告: htmlcov/index.html")
        print("📁 XML报告: coverage.xml")
        print("📁 JUnit报告: test-results.xml")
        return True
    except subprocess.CalledProcessError:
        print("❌ 测试报告生成失败")
        return False


def check_test_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 7):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查项目结构
    required_dirs = ['rds_migrate', 'tests']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"❌ 缺少目录: {dir_name}")
            return False
    
    print("✅ 项目结构正确")
    
    # 检查测试文件
    test_files = [
        'tests/test_config.py',
        'tests/test_database.py',
        'tests/test_migrator.py',
        'tests/test_cli.py',
        'tests/test_performance.py',
        'tests/test_integration.py'
    ]
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"❌ 缺少测试文件: {test_file}")
            return False
    
    print("✅ 测试文件完整")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RDS迁移工具测试运行器')
    parser.add_argument('--type', choices=['unit', 'integration', 'performance', 'all'], 
                       default='all', help='测试类型')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--coverage', '-c', action='store_true', help='生成覆盖率报告')
    parser.add_argument('--skip-slow', action='store_true', help='跳过慢速测试')
    parser.add_argument('--install-deps', action='store_true', help='安装测试依赖')
    parser.add_argument('--report', action='store_true', help='生成详细测试报告')
    parser.add_argument('--test', help='运行特定测试文件或测试函数')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🧪 RDS迁移工具测试套件")
    print("=" * 60)
    
    # 检查测试环境
    if not check_test_environment():
        print("❌ 测试环境检查失败")
        return 1
    
    # 安装测试依赖
    if args.install_deps:
        if not install_test_dependencies():
            return 1
    
    success = True
    
    # 运行特定测试
    if args.test:
        success = run_specific_test(args.test, args.verbose)
    # 生成测试报告
    elif args.report:
        success = generate_test_report()
    # 运行指定类型的测试
    elif args.type == 'unit':
        success = run_unit_tests(args.verbose, args.coverage)
    elif args.type == 'integration':
        success = run_integration_tests(args.verbose)
    elif args.type == 'performance':
        success = run_performance_tests(args.verbose)
    elif args.type == 'all':
        success = run_all_tests(args.verbose, args.coverage, args.skip_slow)
    
    if success:
        print("\n🎉 测试完成！")
        return 0
    else:
        print("\n💥 测试失败！")
        return 1


if __name__ == '__main__':
    sys.exit(main())
