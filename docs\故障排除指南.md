# RDS 数据库迁移工具 - 故障排除指南

## 📋 概述

本指南提供了使用RDS数据库迁移工具时可能遇到的常见问题及其解决方案。按照问题类型分类，帮助您快速定位和解决问题。

## 🔗 连接问题

### 1. 数据库连接失败

#### 症状
```
DatabaseConnectionError: Failed to connect to database
Connection refused
Timeout error
```

#### 可能原因和解决方案

##### 网络连接问题
```bash
# 测试网络连通性
ping database-host.example.com
telnet database-host.example.com 3306

# 检查防火墙设置
# Windows
netsh advfirewall firewall show rule name="MySQL"

# Linux
sudo ufw status
sudo iptables -L
```

##### 数据库服务未运行
```bash
# MySQL
sudo systemctl status mysql
sudo systemctl start mysql

# PostgreSQL
sudo systemctl status postgresql
sudo systemctl start postgresql
```

##### 配置错误
```yaml
# 检查配置文件中的连接参数
source:
  host: "正确的主机名或IP"
  port: 3306  # 确认端口号正确
  username: "有效的用户名"
  password: "正确的密码"
  database: "存在的数据库名"
  db_type: "mysql"  # 确认数据库类型正确
```

### 2. SSL/TLS 连接问题

#### 症状
```
SSL connection error
Certificate verification failed
```

#### 解决方案

##### PostgreSQL SSL配置
```yaml
target:
  host: "postgres-server.com"
  port: 5432
  db_type: "postgresql"
  ssl_mode: "require"  # 可选: disable, allow, prefer, require
```

##### MySQL SSL配置
```yaml
source:
  host: "mysql-server.com"
  port: 3306
  db_type: "mysql"
  ssl_mode: "REQUIRED"  # 可选: DISABLED, PREFERRED, REQUIRED
```

## 🔐 权限问题

### 1. 访问被拒绝

#### 症状
```
Access denied for user 'username'@'host'
Permission denied
Insufficient privileges
```

#### 解决方案

##### 检查用户权限
```sql
-- MySQL
SHOW GRANTS FOR 'username'@'host';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX ON database.* TO 'username'@'host';

-- PostgreSQL
\du username
GRANT ALL PRIVILEGES ON DATABASE database_name TO username;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO username;
```

##### 最小权限要求

**源数据库权限**:
- `SELECT` - 读取数据
- `SHOW VIEW` - 查看视图定义 (MySQL)
- `USAGE` - 连接数据库 (PostgreSQL)

**目标数据库权限**:
- `CREATE` - 创建表和索引
- `INSERT` - 插入数据
- `UPDATE` - 更新数据 (如果需要)
- `DELETE` - 删除数据 (如果需要)
- `DROP` - 删除表 (如果需要重建)

### 2. 表级权限问题

#### 症状
```
Table 'database.table_name' doesn't exist
Access denied for table 'table_name'
```

#### 解决方案
```sql
-- MySQL - 授予特定表权限
GRANT SELECT ON database.table_name TO 'username'@'host';
GRANT INSERT, UPDATE, DELETE ON target_database.table_name TO 'username'@'host';

-- PostgreSQL - 授予表权限
GRANT SELECT ON table_name TO username;
GRANT INSERT, UPDATE, DELETE ON table_name TO username;
```

## 💾 内存和性能问题

### 1. 内存不足

#### 症状
```
MemoryError
Out of memory
System becomes slow/unresponsive
```

#### 解决方案

##### 调整批次大小
```yaml
# 减小批次大小
batch_size: 500  # 从1000减少到500

# 对于大表，可以进一步减小
batch_size: 100
```

##### 减少并行度
```yaml
# 减少并行工作线程
parallel_workers: 2  # 从4减少到2
```

##### 监控内存使用
```bash
# Linux
free -h
htop

# Windows
taskmgr
```

### 2. 迁移速度慢

#### 症状
- 迁移进度缓慢
- 数据传输速度低
- 长时间无响应

#### 解决方案

##### 优化批次大小
```yaml
# 对于小表
batch_size: 1000

# 对于中等表
batch_size: 5000

# 对于大表
batch_size: 10000
```

##### 调整并行度
```yaml
# 根据CPU核心数调整
parallel_workers: 8  # CPU核心数的1-2倍
```

##### 网络优化
```bash
# 检查网络延迟
ping -c 10 database-host.com

# 检查带宽
iperf3 -c database-host.com
```

## 🗄️ 数据库特定问题

### 1. MySQL 问题

#### 字符集问题
```yaml
# 确保字符集配置正确
source:
  charset: "utf8mb4"  # 支持完整的UTF-8
target:
  charset: "utf8mb4"
```

#### 时区问题
```sql
-- 检查时区设置
SELECT @@global.time_zone, @@session.time_zone;

-- 设置时区
SET time_zone = '+00:00';
```

#### 大数据包问题
```sql
-- 增加最大数据包大小
SET GLOBAL max_allowed_packet = 1073741824;  -- 1GB
```

### 2. PostgreSQL 问题

#### 连接数限制
```sql
-- 检查连接数
SELECT count(*) FROM pg_stat_activity;

-- 查看最大连接数
SHOW max_connections;
```

#### 序列问题
```sql
-- 重置序列
SELECT setval('table_id_seq', (SELECT MAX(id) FROM table_name));
```

### 3. SQL Server 问题

#### ODBC驱动问题
```bash
# 安装ODBC Driver 17 for SQL Server
# Ubuntu/Debian
curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list
apt-get update
ACCEPT_EULA=Y apt-get install msodbcsql17

# Windows
# 从Microsoft官网下载并安装ODBC Driver 17
```

#### 身份验证问题
```yaml
# SQL Server身份验证
target:
  host: "sqlserver-host"
  port: 1433
  username: "sa"
  password: "password"
  db_type: "sqlserver"

# Windows身份验证
target:
  host: "sqlserver-host"
  port: 1433
  username: ""  # 空用户名
  password: ""  # 空密码
  db_type: "sqlserver"
```

## 🔧 配置问题

### 1. 配置文件格式错误

#### 症状
```
YAML parsing error
Invalid configuration format
```

#### 解决方案
```bash
# 验证YAML格式
python -c "import yaml; yaml.safe_load(open('config.yaml'))"

# 使用在线YAML验证器
# https://yamlchecker.com/
```

### 2. 配置参数错误

#### 症状
```
ConfigurationError: Invalid parameter
Unknown configuration option
```

#### 解决方案
```bash
# 验证配置文件
python -m rds_migrate.cli validate --config config.yaml

# 查看配置模板
python -m rds_migrate.cli init --name template
```

## 🐛 调试技巧

### 1. 启用详细日志

```bash
# 设置日志级别为DEBUG
export LOG_LEVEL=DEBUG
python -m rds_migrate.cli migrate --config config.yaml

# 或者在命令中指定
python -m rds_migrate.cli migrate --config config.yaml --log-level DEBUG
```

### 2. 干运行测试

```bash
# 执行干运行，不实际迁移数据
python -m rds_migrate.cli migrate --config config.yaml --dry-run
```

### 3. 单表测试

```yaml
# 在配置文件中只指定一个小表进行测试
tables:
  - "small_test_table"
```

### 4. 分步调试

```bash
# 1. 测试连接
python -m rds_migrate.cli test --config config.yaml

# 2. 分析数据库
python -m rds_migrate.cli analyze --config config.yaml

# 3. 验证配置
python -m rds_migrate.cli validate --config config.yaml

# 4. 执行迁移
python -m rds_migrate.cli migrate --config config.yaml
```

## 📊 监控和诊断

### 1. 性能监控

```bash
# 监控系统资源
top
htop
iostat -x 1

# 监控网络
netstat -i
iftop
```

### 2. 数据库监控

```sql
-- MySQL
SHOW PROCESSLIST;
SHOW ENGINE INNODB STATUS;

-- PostgreSQL
SELECT * FROM pg_stat_activity;
SELECT * FROM pg_stat_database;
```

### 3. 日志分析

```bash
# 查看迁移日志
tail -f logs/migration.log

# 搜索错误信息
grep -i error logs/migration.log
grep -i warning logs/migration.log
```

## 🆘 获取帮助

### 1. 收集诊断信息

在寻求帮助时，请提供以下信息：

```bash
# 系统信息
python --version
pip list | grep -E "(sqlalchemy|pymysql|psycopg2|pyodbc)"

# 配置文件 (移除敏感信息)
cat config.yaml

# 错误日志
tail -50 logs/migration.log

# 系统资源
free -h
df -h
```

### 2. 联系支持

- 📖 **查看文档**: `docs/` 目录中的详细文档
- 🐛 **报告问题**: 在GitHub Issues中提交问题
- 💬 **社区讨论**: 在GitHub Discussions中参与讨论
- 📧 **技术支持**: 通过项目仓库联系开发团队

### 3. 问题报告模板

```markdown
## 问题描述
[简要描述遇到的问题]

## 环境信息
- 操作系统: [Windows/Linux/macOS]
- Python版本: [3.8/3.9/3.10/3.11]
- 源数据库: [MySQL 8.0/PostgreSQL 13/等]
- 目标数据库: [MySQL 8.0/PostgreSQL 13/等]

## 配置文件
```yaml
[粘贴配置文件，移除敏感信息]
```

## 错误信息
```
[粘贴完整的错误信息和堆栈跟踪]
```

## 重现步骤
1. [步骤1]
2. [步骤2]
3. [步骤3]

## 预期行为
[描述期望的正确行为]

## 实际行为
[描述实际发生的行为]
```

---

*本文档最后更新: 2025-01-26*
