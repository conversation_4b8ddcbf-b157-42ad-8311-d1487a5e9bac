"""
Database connection management and operations.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from contextlib import contextmanager
import pymysql
import psycopg2
import pyodbc
from sqlalchemy import create_engine, text, MetaData, Table
from sqlalchemy.engine import Engine
from .config import DatabaseConfig

logger = logging.getLogger(__name__)


class DatabaseConnectionError(Exception):
    """Database connection related errors."""
    pass


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.engine: Optional[Engine] = None
        self._connection_string = self._build_connection_string()
    
    def _build_connection_string(self) -> str:
        """Build SQLAlchemy connection string based on database type."""
        config = self.config
        
        if config.db_type == 'mysql':
            return (f"mysql+pymysql://{config.username}:{config.password}@"
                   f"{config.host}:{config.port}/{config.database}"
                   f"?charset={config.charset or 'utf8mb4'}")
        
        elif config.db_type == 'postgresql':
            ssl_mode = f"?sslmode={config.ssl_mode}" if config.ssl_mode else ""
            return (f"postgresql+psycopg2://{config.username}:{config.password}@"
                   f"{config.host}:{config.port}/{config.database}{ssl_mode}")
        
        elif config.db_type == 'sqlserver':
            driver = "ODBC+Driver+17+for+SQL+Server"
            return (f"mssql+pyodbc://{config.username}:{config.password}@"
                   f"{config.host}:{config.port}/{config.database}?driver={driver}")
        
        elif config.db_type == 'oracle':
            return (f"oracle+cx_oracle://{config.username}:{config.password}@"
                   f"{config.host}:{config.port}/{config.database}")
        
        else:
            raise ValueError(f"Unsupported database type: {config.db_type}")
    
    def connect(self) -> Engine:
        """Establish database connection."""
        try:
            self.engine = create_engine(
                self._connection_string,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
            
            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            logger.info(f"Connected to {self.config.db_type} database: {self.config.host}")
            return self.engine
            
        except Exception as e:
            raise DatabaseConnectionError(f"Failed to connect to database: {str(e)}")
    
    def disconnect(self):
        """Close database connection."""
        if self.engine:
            self.engine.dispose()
            self.engine = None
            logger.info("Database connection closed")
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections."""
        if not self.engine:
            self.connect()
        
        conn = self.engine.connect()
        try:
            yield conn
        finally:
            conn.close()
    
    def test_connection(self) -> bool:
        """Test database connectivity."""
        try:
            if not self.engine:
                self.connect()
            
            with self.get_connection() as conn:
                conn.execute(text("SELECT 1"))
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False
    
    def get_table_list(self) -> List[str]:
        """Get list of all tables in the database."""
        try:
            with self.get_connection() as conn:
                metadata = MetaData()
                metadata.reflect(bind=conn)
                return list(metadata.tables.keys())
                
        except Exception as e:
            logger.error(f"Failed to get table list: {str(e)}")
            raise
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get table schema information."""
        try:
            with self.get_connection() as conn:
                metadata = MetaData()
                table = Table(table_name, metadata, autoload_with=conn)
                
                columns = []
                for column in table.columns:
                    columns.append({
                        'name': column.name,
                        'type': str(column.type),
                        'nullable': column.nullable,
                        'primary_key': column.primary_key,
                        'default': str(column.default) if column.default else None
                    })
                
                return {
                    'table_name': table_name,
                    'columns': columns,
                    'primary_keys': [col.name for col in table.primary_key.columns],
                    'indexes': [idx.name for idx in table.indexes]
                }
                
        except Exception as e:
            logger.error(f"Failed to get schema for table {table_name}: {str(e)}")
            raise
    
    def get_row_count(self, table_name: str) -> int:
        """Get row count for a table."""
        try:
            with self.get_connection() as conn:
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                return result.scalar()
                
        except Exception as e:
            logger.error(f"Failed to get row count for {table_name}: {str(e)}")
            raise
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> Any:
        """Execute a SQL query."""
        try:
            with self.get_connection() as conn:
                return conn.execute(text(query), params or {})
                
        except Exception as e:
            logger.error(f"Failed to execute query: {str(e)}")
            raise
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get database information."""
        info = {
            'db_type': self.config.db_type,
            'host': self.config.host,
            'port': self.config.port,
            'database': self.config.database,
            'connected': self.engine is not None
        }
        
        try:
            if self.config.db_type == 'mysql':
                with self.get_connection() as conn:
                    result = conn.execute(text("SELECT VERSION()"))
                    info['version'] = result.scalar()
                    
            elif self.config.db_type == 'postgresql':
                with self.get_connection() as conn:
                    result = conn.execute(text("SELECT version()"))
                    info['version'] = result.scalar()
                    
            elif self.config.db_type == 'sqlserver':
                with self.get_connection() as conn:
                    result = conn.execute(text("SELECT @@VERSION"))
                    info['version'] = result.scalar()
                    
        except Exception as e:
            logger.warning(f"Could not get database version: {str(e)}")
            info['version'] = 'Unknown'
        
        return info
