"""
迁移引擎模块测试
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from rds_migrate.migrator import MigrationEngine
from rds_migrate.config import MigrationConfig, DatabaseConfig


@pytest.mark.unit
@pytest.mark.migration
class TestMigrationEngine:
    """迁移引擎测试"""
    
    @pytest.fixture
    def migration_config(self):
        """迁移配置"""
        source_config = DatabaseConfig(
            db_type="mysql",
            host="source.example.com",
            port=3306,
            database="source_db",
            username="user",
            password="pass"
        )
        
        target_config = DatabaseConfig(
            db_type="mysql",
            host="target.example.com",
            port=3306,
            database="target_db",
            username="user",
            password="pass"
        )
        
        return MigrationConfig(
            source=source_config,
            target=target_config,
            batch_size=1000,
            parallel_workers=2,
            tables=["users", "orders"],
            exclude_tables=["temp_table"],
            schema_only=False,
            data_only=False,
            create_indexes=True,
            create_foreign_keys=True
        )
    
    def test_migration_engine_creation(self, migration_config):
        """测试迁移引擎创建"""
        engine = MigrationEngine(migration_config)

        assert engine.config == migration_config
        assert engine.source_db is not None
        assert engine.target_db is not None
        assert engine.stats is not None
        assert engine.progress_callback is None
        assert engine._connection_cache == {}
        assert engine._metadata_cache == {}
    
    def test_set_progress_callback(self, migration_config):
        """测试设置进度回调"""
        engine = MigrationEngine(migration_config)
        callback = Mock()

        engine.set_progress_callback(callback)

        assert engine.progress_callback == callback

    @patch('rds_migrate.migrator.MigrationEngine._verify_connections')
    def test_connect_databases_success(self, mock_verify, migration_config):
        """测试成功连接数据库"""
        engine = MigrationEngine(migration_config)
        engine.source_db = Mock()
        engine.target_db = Mock()

        engine.connect_databases()

        engine.source_db.connect.assert_called_once()
        engine.target_db.connect.assert_called_once()
        mock_verify.assert_called_once()

    def test_connect_databases_failure(self, migration_config):
        """测试连接数据库失败"""
        engine = MigrationEngine(migration_config)
        engine.source_db = Mock()
        engine.target_db = Mock()
        engine.source_db.connect.side_effect = Exception("Connection failed")

        with pytest.raises(Exception):
            engine.connect_databases()

    def test_disconnect_databases(self, migration_config):
        """测试断开数据库连接"""
        engine = MigrationEngine(migration_config)
        engine.source_db = Mock()
        engine.target_db = Mock()

        engine.disconnect_databases()

        engine.source_db.disconnect.assert_called_once()
        engine.target_db.disconnect.assert_called_once()
    
    def test_disconnect_databases(self, migration_config):
        """测试断开数据库连接"""
        engine = MigrationEngine(migration_config)
        engine.source_db = Mock()
        engine.target_db = Mock()

        engine.disconnect_databases()

        engine.source_db.disconnect.assert_called_once()
        engine.target_db.disconnect.assert_called_once()
    
    def test_get_tables_to_migrate_all(self, migration_config):
        """测试获取要迁移的表 - 全部表"""
        migration_config.tables = []  # 空列表表示所有表
        
        engine = MigrationEngine(migration_config)
        engine.source_db = Mock()
        engine.source_db.get_table_list.return_value = ["users", "orders", "products", "temp_table"]
        
        tables = engine.get_tables_to_migrate()
        
        # 应该排除 exclude_tables 中的表
        expected = ["users", "orders", "products"]
        assert set(tables) == set(expected)
    
    def test_get_tables_to_migrate_specific(self, migration_config):
        """测试获取要迁移的表 - 指定表"""
        engine = MigrationEngine(migration_config)
        
        tables = engine.get_tables_to_migrate()
        
        assert tables == ["users", "orders"]
    
    def test_migrate_table_schema(self, migration_config):
        """测试迁移表结构"""
        engine = MigrationEngine(migration_config)

        with patch.object(engine, '_get_source_table_metadata') as mock_get_metadata, \
             patch.object(engine, '_create_target_table') as mock_create_table:

            mock_table = Mock()
            mock_get_metadata.return_value = mock_table
            mock_create_table.return_value = True

            result = engine.migrate_table_schema("users")

            assert result == True
            mock_get_metadata.assert_called_once_with("users")
            mock_create_table.assert_called_once_with("users", mock_table)

    def test_migrate_table_schema_failure(self, migration_config):
        """测试迁移表结构失败"""
        engine = MigrationEngine(migration_config)

        # 使用Mock对象替换数据库管理器
        engine.target_db = Mock()

        with patch.object(engine, '_get_source_table_metadata') as mock_get_metadata:
            mock_get_metadata.side_effect = Exception("Schema error")

            result = engine.migrate_table_schema("users")

            assert result == False
            # 验证create_table没有被调用（因为获取元数据失败）
            engine.target_db.create_table.assert_not_called()
    
    def test_migrate_table_data(self, migration_config):
        """测试迁移表数据"""
        engine = MigrationEngine(migration_config)
        engine.source_db = Mock()
        engine.target_db = Mock()

        # 正确配置Mock对象
        engine.source_db.get_row_count.return_value = 2500
        engine.source_db.get_table_schema.return_value = {
            "columns": [
                {"name": "id", "type": "INTEGER"},
                {"name": "name", "type": "VARCHAR(255)"}
            ]
        }

        # 模拟源数据库连接和查询
        mock_source_conn = Mock()
        mock_result = Mock()
        mock_result.fetchmany.side_effect = [
            [("1", "Alice"), ("2", "Bob")],  # 第一批
            [("3", "Charlie")],  # 第二批
            []  # 结束
        ]
        mock_source_conn.execute.return_value = mock_result

        # 配置源数据库上下文管理器
        mock_source_context = Mock()
        mock_source_context.__enter__ = Mock(return_value=mock_source_conn)
        mock_source_context.__exit__ = Mock(return_value=None)
        engine.source_db.get_connection.return_value = mock_source_context

        # 模拟目标数据库连接和插入成功
        mock_target_conn = Mock()
        mock_target_conn.execute.return_value = None
        mock_target_conn.commit.return_value = None

        # 正确配置上下文管理器
        mock_context_manager = Mock()
        mock_context_manager.__enter__ = Mock(return_value=mock_target_conn)
        mock_context_manager.__exit__ = Mock(return_value=None)
        engine.target_db.get_connection.return_value = mock_context_manager

        engine.target_db.insert_data.return_value = True

        result = engine.migrate_table_data("users")

        assert result == True
        # 验证插入数据被调用
        assert engine.target_db.insert_data.call_count >= 1
    
    def test_migrate_table_data_with_progress(self, migration_config):
        """测试带进度回调的数据迁移"""
        progress_calls = []

        def progress_callback(current, total, message):
            progress_calls.append((current, total, message))

        engine = MigrationEngine(migration_config)
        engine.progress_callback = progress_callback
        engine.source_db = Mock()
        engine.target_db = Mock()

        # 正确配置Mock对象
        engine.source_db.get_row_count.return_value = 1000
        engine.source_db.get_table_schema.return_value = {
            "columns": [
                {"name": "id", "type": "INTEGER"}
            ]
        }
        engine.source_db.execute_query.side_effect = [
            [{"id": i} for i in range(100)],  # 第一批数据
            [{"id": i} for i in range(100, 200)],  # 第二批数据
            []  # 结束
        ]

        # 模拟目标数据库插入成功
        engine.target_db.insert_data.return_value = True

        result = engine.migrate_table_data("users")

        assert result == True
        assert len(progress_calls) > 0
    
    def test_migrate_success(self, migration_config):
        """测试完整迁移成功"""
        engine = MigrationEngine(migration_config)

        # 模拟所有方法成功
        with patch.object(engine, 'connect_databases'), \
             patch.object(engine, 'get_tables_to_migrate', return_value=["users", "orders"]), \
             patch.object(engine, 'migrate_table', return_value=True), \
             patch.object(engine, 'disconnect_databases'):

            result = engine.migrate()

            assert result.tables_migrated == 2
    
    def test_migrate_no_tables(self, migration_config):
        """测试没有表需要迁移"""
        engine = MigrationEngine(migration_config)

        with patch.object(engine, 'connect_databases'), \
             patch.object(engine, 'get_tables_to_migrate', return_value=[]), \
             patch.object(engine, 'disconnect_databases'):

            result = engine.migrate()

            assert result.tables_migrated == 0
