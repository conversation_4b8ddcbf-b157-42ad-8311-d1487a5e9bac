"""
Logging and progress monitoring for RDS migration tool.
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from colorama import init, Fore, Back, Style
from tqdm import tqdm

# Initialize colorama for cross-platform colored output
init(autoreset=True)


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colored output."""
    
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Back.WHITE + Style.BRIGHT
    }
    
    def format(self, record):
        # Add color to levelname
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{Style.RESET_ALL}"
        
        return super().format(record)


class MigrationLogger:
    """Enhanced logger for migration operations."""
    
    def __init__(self, log_level: str = "INFO", log_file: Optional[str] = None):
        self.log_level = getattr(logging, log_level.upper())
        self.log_file = log_file
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging configuration."""
        # Create logger
        self.logger = logging.getLogger('rds_migrate')
        self.logger.setLevel(self.log_level)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler with colors
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler (if specified)
        if self.log_file:
            # Ensure log directory exists
            Path(self.log_file).parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)  # Always log everything to file
            
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def get_logger(self) -> logging.Logger:
        """Get the configured logger."""
        return self.logger


class ProgressTracker:
    """Progress tracking for migration operations."""
    
    def __init__(self):
        self.current_table: Optional[str] = None
        self.table_progress: Dict[str, Dict[str, Any]] = {}
        self.overall_progress: Optional[tqdm] = None
        self.table_progress_bar: Optional[tqdm] = None
    
    def start_overall_progress(self, total_tables: int):
        """Start overall progress tracking."""
        self.overall_progress = tqdm(
            total=total_tables,
            desc="Overall Progress",
            position=0,
            bar_format='{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} tables'
        )
    
    def start_table_progress(self, table_name: str, total_rows: int):
        """Start progress tracking for a specific table."""
        self.current_table = table_name
        self.table_progress[table_name] = {
            'total_rows': total_rows,
            'migrated_rows': 0,
            'start_time': datetime.now(),
            'status': 'in_progress'
        }
        
        if self.table_progress_bar:
            self.table_progress_bar.close()
        
        self.table_progress_bar = tqdm(
            total=total_rows,
            desc=f"Migrating {table_name}",
            position=1,
            leave=False,
            bar_format='{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} rows [{elapsed}<{remaining}]'
        )
    
    def update_table_progress(self, table_name: str, migrated_rows: int, total_rows: int):
        """Update progress for current table."""
        if table_name in self.table_progress:
            self.table_progress[table_name]['migrated_rows'] = migrated_rows
        
        if self.table_progress_bar and table_name == self.current_table:
            self.table_progress_bar.n = migrated_rows
            self.table_progress_bar.refresh()
    
    def complete_table_progress(self, table_name: str, success: bool = True):
        """Complete progress tracking for a table."""
        if table_name in self.table_progress:
            self.table_progress[table_name]['status'] = 'completed' if success else 'failed'
            self.table_progress[table_name]['end_time'] = datetime.now()
        
        if self.table_progress_bar:
            self.table_progress_bar.close()
            self.table_progress_bar = None
        
        if self.overall_progress:
            self.overall_progress.update(1)
    
    def complete_overall_progress(self):
        """Complete overall progress tracking."""
        if self.overall_progress:
            self.overall_progress.close()
            self.overall_progress = None
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get progress summary."""
        completed = sum(1 for p in self.table_progress.values() if p['status'] == 'completed')
        failed = sum(1 for p in self.table_progress.values() if p['status'] == 'failed')
        total_rows = sum(p['total_rows'] for p in self.table_progress.values())
        migrated_rows = sum(p['migrated_rows'] for p in self.table_progress.values())
        
        return {
            'total_tables': len(self.table_progress),
            'completed_tables': completed,
            'failed_tables': failed,
            'total_rows': total_rows,
            'migrated_rows': migrated_rows,
            'progress_percentage': (migrated_rows / total_rows * 100) if total_rows > 0 else 0
        }


class MigrationReporter:
    """Generate migration reports."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def print_database_info(self, db_info: Dict[str, Any], db_type: str):
        """Print database information."""
        self.logger.info(f"\n{db_type} Database Information:")
        self.logger.info(f"  Type: {db_info.get('db_type', 'Unknown')}")
        self.logger.info(f"  Host: {db_info.get('host', 'Unknown')}")
        self.logger.info(f"  Port: {db_info.get('port', 'Unknown')}")
        self.logger.info(f"  Database: {db_info.get('database', 'Unknown')}")
        self.logger.info(f"  Version: {db_info.get('version', 'Unknown')}")
        self.logger.info(f"  Connected: {db_info.get('connected', False)}")
    
    def print_migration_summary(self, stats, progress_tracker: ProgressTracker):
        """Print migration summary."""
        summary = progress_tracker.get_progress_summary()
        
        self.logger.info("\n" + "="*60)
        self.logger.info("MIGRATION SUMMARY")
        self.logger.info("="*60)
        
        self.logger.info(f"Duration: {stats.duration:.2f} seconds")
        self.logger.info(f"Tables processed: {summary['total_tables']}")
        self.logger.info(f"Tables completed: {summary['completed_tables']}")
        self.logger.info(f"Tables failed: {summary['failed_tables']}")
        self.logger.info(f"Total rows migrated: {summary['migrated_rows']:,}")
        self.logger.info(f"Migration errors: {stats.errors}")
        
        if stats.duration > 0:
            rows_per_second = summary['migrated_rows'] / stats.duration
            self.logger.info(f"Average speed: {rows_per_second:.2f} rows/second")
        
        # Print table details
        if progress_tracker.table_progress:
            self.logger.info("\nTable Details:")
            for table_name, progress in progress_tracker.table_progress.items():
                status_color = Fore.GREEN if progress['status'] == 'completed' else Fore.RED
                self.logger.info(f"  {table_name}: {status_color}{progress['status']}{Style.RESET_ALL} "
                               f"({progress['migrated_rows']:,}/{progress['total_rows']:,} rows)")
        
        self.logger.info("="*60)
    
    def save_migration_report(self, stats, progress_tracker: ProgressTracker, 
                            output_file: str = "migration_report.txt"):
        """Save detailed migration report to file."""
        try:
            summary = progress_tracker.get_progress_summary()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("RDS MIGRATION REPORT\n")
                f.write("="*50 + "\n\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("SUMMARY\n")
                f.write("-"*20 + "\n")
                f.write(f"Duration: {stats.duration:.2f} seconds\n")
                f.write(f"Tables processed: {summary['total_tables']}\n")
                f.write(f"Tables completed: {summary['completed_tables']}\n")
                f.write(f"Tables failed: {summary['failed_tables']}\n")
                f.write(f"Total rows migrated: {summary['migrated_rows']:,}\n")
                f.write(f"Migration errors: {stats.errors}\n")
                
                if stats.duration > 0:
                    rows_per_second = summary['migrated_rows'] / stats.duration
                    f.write(f"Average speed: {rows_per_second:.2f} rows/second\n")
                
                f.write("\nTABLE DETAILS\n")
                f.write("-"*20 + "\n")
                for table_name, progress in progress_tracker.table_progress.items():
                    duration = 0
                    if 'start_time' in progress and 'end_time' in progress:
                        duration = (progress['end_time'] - progress['start_time']).total_seconds()
                    
                    f.write(f"Table: {table_name}\n")
                    f.write(f"  Status: {progress['status']}\n")
                    f.write(f"  Rows: {progress['migrated_rows']:,}/{progress['total_rows']:,}\n")
                    f.write(f"  Duration: {duration:.2f} seconds\n")
                    if duration > 0 and progress['migrated_rows'] > 0:
                        speed = progress['migrated_rows'] / duration
                        f.write(f"  Speed: {speed:.2f} rows/second\n")
                    f.write("\n")
            
            self.logger.info(f"Migration report saved to: {output_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save migration report: {str(e)}")
