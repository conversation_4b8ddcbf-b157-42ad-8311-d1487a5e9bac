"""
性能测试
"""

import pytest
import time
import threading
import asyncio
from unittest.mock import Mock, patch
import statistics

from rds_migrate.migrator import MigrationEngine
from rds_migrate.config import MigrationConfig, DatabaseConfig


@pytest.mark.performance
class TestPerformance:
    """性能测试"""
    
    @pytest.fixture
    def performance_config(self):
        """性能测试配置"""
        source_config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="perf_source",
            username="test",
            password="test"
        )
        
        target_config = DatabaseConfig(
            db_type="mysql",
            host="localhost",
            port=3306,
            database="perf_target",
            username="test",
            password="test"
        )
        
        return MigrationConfig(
            source=source_config,
            target=target_config,
            batch_size=1000,
            parallel_workers=4,
            tables=["large_table"],
            schema_only=False,
            data_only=False
        )
    
    @pytest.mark.slow
    def test_large_data_migration_performance(self, performance_config):
        """测试大数据量迁移性能"""
        engine = MigrationEngine(performance_config)
        
        # 模拟大量数据
        large_dataset_size = 100000
        batch_size = 1000
        
        with patch.object(engine, 'source_db') as mock_source, \
             patch.object(engine, 'target_db') as mock_target:
            
            # 模拟数据源
            mock_source.get_table_count.return_value = large_dataset_size
            
            # 生成批次数据
            def generate_batch_data(offset, limit):
                return [{"id": i, "data": f"test_data_{i}"} for i in range(offset, min(offset + limit, large_dataset_size))]
            
            # 模拟批次查询
            call_count = 0
            def mock_execute_query(query):
                nonlocal call_count
                offset = call_count * batch_size
                call_count += 1
                if offset >= large_dataset_size:
                    return []
                return generate_batch_data(offset, batch_size)
            
            mock_source.execute_query.side_effect = mock_execute_query
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行迁移
            result = engine.migrate_table_data("large_table")
            
            # 记录结束时间
            end_time = time.time()
            duration = end_time - start_time
            
            assert result == True
            
            # 性能断言
            records_per_second = large_dataset_size / duration
            print(f"迁移性能: {records_per_second:.2f} 记录/秒")
            print(f"总耗时: {duration:.2f} 秒")
            
            # 期望至少每秒处理1000条记录
            assert records_per_second > 1000, f"性能不达标: {records_per_second:.2f} 记录/秒"
    
    def test_concurrent_table_migration(self, performance_config):
        """测试并发表迁移性能"""
        performance_config.tables = ["table1", "table2", "table3", "table4"]
        performance_config.parallel_workers = 4
        
        engine = MigrationEngine(performance_config)
        
        migration_times = []
        
        def mock_migrate_table(table_name):
            """模拟表迁移，记录时间"""
            start = time.time()
            time.sleep(0.1)  # 模拟迁移耗时
            end = time.time()
            migration_times.append(end - start)
            return True
        
        with patch.object(engine, 'migrate_table_schema', return_value=True), \
             patch.object(engine, 'migrate_table_data', side_effect=mock_migrate_table), \
             patch.object(engine, 'disconnect_databases'):
            
            start_time = time.time()
            result = engine.migrate()
            end_time = time.time()
            
            total_duration = end_time - start_time
            
            assert result == True
            assert len(migration_times) == 4
            
            # 并发执行应该比串行快
            sequential_time = sum(migration_times)
            speedup = sequential_time / total_duration
            
            print(f"并发加速比: {speedup:.2f}x")
            print(f"总耗时: {total_duration:.2f} 秒")
            print(f"串行耗时: {sequential_time:.2f} 秒")
            
            # 期望至少有1.5倍的加速
            assert speedup > 1.5, f"并发性能不达标: {speedup:.2f}x"
    
    def test_memory_usage_during_migration(self, performance_config):
        """测试迁移过程中的内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        engine = MigrationEngine(performance_config)
        
        # 模拟大批次数据
        large_batch_size = 10000
        performance_config.batch_size = large_batch_size
        
        with patch.object(engine, 'source_db') as mock_source, \
             patch.object(engine, 'target_db') as mock_target:
            
            # 模拟大量数据
            large_data = [{"id": i, "data": "x" * 1000} for i in range(large_batch_size)]
            mock_source.get_table_count.return_value = large_batch_size
            mock_source.execute_query.side_effect = [large_data, []]
            
            # 执行迁移
            result = engine.migrate_table_data("large_table")
            
            # 检查内存使用
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            assert result == True
            
            print(f"初始内存: {initial_memory:.2f} MB")
            print(f"峰值内存: {peak_memory:.2f} MB")
            print(f"内存增长: {memory_increase:.2f} MB")
            
            # 内存增长应该在合理范围内（小于500MB）
            assert memory_increase < 500, f"内存使用过多: {memory_increase:.2f} MB"
    
    def test_connection_pool_performance(self, performance_config):
        """测试连接池性能"""
        from rds_migrate.database import DatabaseManager
        
        # 测试多次连接的性能
        connection_times = []
        
        for i in range(10):
            with patch('rds_migrate.database.create_engine') as mock_create_engine:
                mock_engine = Mock()
                mock_connection = Mock()
                mock_engine.connect.return_value = mock_connection
                mock_create_engine.return_value = mock_engine
                
                manager = DatabaseManager(performance_config.source)
                
                start_time = time.time()
                manager.connect()
                end_time = time.time()
                
                connection_times.append(end_time - start_time)
                manager.disconnect()
        
        avg_connection_time = statistics.mean(connection_times)
        max_connection_time = max(connection_times)
        
        print(f"平均连接时间: {avg_connection_time:.4f} 秒")
        print(f"最大连接时间: {max_connection_time:.4f} 秒")
        
        # 连接时间应该很快（小于0.1秒）
        assert avg_connection_time < 0.1, f"连接时间过长: {avg_connection_time:.4f} 秒"
        assert max_connection_time < 0.2, f"最大连接时间过长: {max_connection_time:.4f} 秒"
    
    def test_batch_size_optimization(self, performance_config):
        """测试批次大小优化"""
        engine = MigrationEngine(performance_config)
        
        batch_sizes = [100, 500, 1000, 2000, 5000]
        performance_results = {}
        
        for batch_size in batch_sizes:
            performance_config.batch_size = batch_size
            
            with patch.object(engine, 'source_db') as mock_source, \
                 patch.object(engine, 'target_db') as mock_target:
                
                # 模拟固定数量的数据
                total_records = 10000
                mock_source.get_table_count.return_value = total_records
                
                # 计算批次数量
                num_batches = (total_records + batch_size - 1) // batch_size
                
                # 模拟批次数据
                batches = []
                for i in range(num_batches):
                    start_idx = i * batch_size
                    end_idx = min(start_idx + batch_size, total_records)
                    batch_data = [{"id": j} for j in range(start_idx, end_idx)]
                    batches.append(batch_data)
                batches.append([])  # 结束标记
                
                mock_source.execute_query.side_effect = batches
                
                # 测量性能
                start_time = time.time()
                result = engine.migrate_table_data("test_table")
                end_time = time.time()
                
                duration = end_time - start_time
                performance_results[batch_size] = duration
                
                assert result == True
        
        # 分析结果
        print("批次大小性能测试结果:")
        for batch_size, duration in performance_results.items():
            records_per_second = 10000 / duration
            print(f"批次大小 {batch_size}: {duration:.4f} 秒, {records_per_second:.2f} 记录/秒")
        
        # 找到最佳批次大小
        best_batch_size = min(performance_results.keys(), key=lambda k: performance_results[k])
        print(f"最佳批次大小: {best_batch_size}")
        
        # 验证性能差异是合理的
        best_time = performance_results[best_batch_size]
        worst_time = max(performance_results.values())
        performance_ratio = worst_time / best_time
        
        assert performance_ratio > 1.0, "批次大小应该对性能有影响"
    
    @pytest.mark.slow
    def test_stress_test(self, performance_config):
        """压力测试"""
        engine = MigrationEngine(performance_config)
        
        # 模拟高并发场景
        num_threads = 10
        operations_per_thread = 100
        results = []
        errors = []
        
        def worker_thread():
            """工作线程"""
            try:
                for i in range(operations_per_thread):
                    with patch.object(engine, 'source_db') as mock_source, \
                         patch.object(engine, 'target_db') as mock_target:
                        
                        mock_source.execute_query.return_value = [{"id": i}]
                        mock_target.insert_data.return_value = None
                        
                        start_time = time.time()
                        # 模拟数据库操作
                        time.sleep(0.001)  # 1ms操作
                        end_time = time.time()
                        
                        results.append(end_time - start_time)
            except Exception as e:
                errors.append(str(e))
        
        # 启动多个线程
        threads = []
        start_time = time.time()
        
        for i in range(num_threads):
            thread = threading.Thread(target=worker_thread)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 分析结果
        total_operations = num_threads * operations_per_thread
        operations_per_second = total_operations / total_duration
        avg_operation_time = statistics.mean(results) if results else 0
        
        print(f"压力测试结果:")
        print(f"总操作数: {total_operations}")
        print(f"总耗时: {total_duration:.2f} 秒")
        print(f"操作/秒: {operations_per_second:.2f}")
        print(f"平均操作时间: {avg_operation_time:.4f} 秒")
        print(f"错误数: {len(errors)}")
        
        # 验证性能和稳定性
        assert len(errors) == 0, f"压力测试中出现错误: {errors}"
        assert operations_per_second > 100, f"操作性能不达标: {operations_per_second:.2f} ops/sec"
        assert avg_operation_time < 0.1, f"平均操作时间过长: {avg_operation_time:.4f} 秒"
