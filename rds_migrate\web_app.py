"""
现代化Web界面 - 使用FastAPI + WebSocket实现实时进度监控
"""

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
import json
import logging
import uuid
from pathlib import Path

from .config import MigrationConfig, DatabaseConfig, ConfigManager
from .async_migrator import AsyncMigrationEngine
from .database import DatabaseManager

logger = logging.getLogger(__name__)

app = FastAPI(title="RDS数据库迁移工具", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
active_migrations: Dict[str, AsyncMigrationEngine] = {}
websocket_connections: List[WebSocket] = []


class DatabaseConfigModel(BaseModel):
    """数据库配置模型"""
    host: str
    port: int
    database: str
    username: str
    password: str
    db_type: str
    charset: Optional[str] = None
    ssl_enabled: bool = False


class MigrationConfigModel(BaseModel):
    """迁移配置模型"""
    source: DatabaseConfigModel
    target: DatabaseConfigModel
    batch_size: int = 1000
    parallel_workers: int = 4
    schema_only: bool = False
    data_only: bool = False
    create_indexes: bool = True
    create_foreign_keys: bool = True
    tables: Optional[List[str]] = None
    exclude_tables: Optional[List[str]] = None


class MigrationRequest(BaseModel):
    """迁移请求模型"""
    config: MigrationConfigModel
    dry_run: bool = False


class ConnectionTestRequest(BaseModel):
    """连接测试请求模型"""
    database_config: DatabaseConfigModel


@app.get("/", response_class=HTMLResponse)
async def get_index():
    """返回主页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RDS数据库迁移工具</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <style>
            .progress-container { display: none; }
            .log-container { max-height: 400px; overflow-y: auto; }
            .status-badge { font-size: 0.8em; }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <nav class="navbar navbar-dark bg-primary">
                <div class="container-fluid">
                    <span class="navbar-brand mb-0 h1">RDS数据库迁移工具</span>
                    <span class="navbar-text" id="status">就绪</span>
                </div>
            </nav>
            
            <div class="row mt-3">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" data-bs-toggle="tab" href="#config-tab">配置管理</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#migration-tab">迁移执行</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#logs-tab">日志监控</a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content">
                                <!-- 配置管理标签页 -->
                                <div class="tab-pane fade show active" id="config-tab">
                                    <form id="migration-form">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h5>源数据库配置</h5>
                                                <div class="mb-3">
                                                    <label class="form-label">数据库类型</label>
                                                    <select class="form-select" name="source_db_type" required>
                                                        <option value="mysql">MySQL</option>
                                                        <option value="postgresql">PostgreSQL</option>
                                                        <option value="sqlserver">SQL Server</option>
                                                        <option value="oracle">Oracle</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">主机地址</label>
                                                    <input type="text" class="form-control" name="source_host" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">端口</label>
                                                    <input type="number" class="form-control" name="source_port" value="3306" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">数据库名</label>
                                                    <input type="text" class="form-control" name="source_database" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">用户名</label>
                                                    <input type="text" class="form-control" name="source_username" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">密码</label>
                                                    <input type="password" class="form-control" name="source_password" required>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary" onclick="testConnection('source')">测试连接</button>
                                            </div>
                                            <div class="col-md-6">
                                                <h5>目标数据库配置</h5>
                                                <div class="mb-3">
                                                    <label class="form-label">数据库类型</label>
                                                    <select class="form-select" name="target_db_type" required>
                                                        <option value="mysql">MySQL</option>
                                                        <option value="postgresql">PostgreSQL</option>
                                                        <option value="sqlserver">SQL Server</option>
                                                        <option value="oracle">Oracle</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">主机地址</label>
                                                    <input type="text" class="form-control" name="target_host" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">端口</label>
                                                    <input type="number" class="form-control" name="target_port" value="3306" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">数据库名</label>
                                                    <input type="text" class="form-control" name="target_database" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">用户名</label>
                                                    <input type="text" class="form-control" name="target_username" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">密码</label>
                                                    <input type="password" class="form-control" name="target_password" required>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary" onclick="testConnection('target')">测试连接</button>
                                            </div>
                                        </div>
                                        
                                        <hr>
                                        <h5>迁移设置</h5>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label class="form-label">批次大小</label>
                                                <input type="number" class="form-control" name="batch_size" value="1000">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">并行工作线程</label>
                                                <input type="number" class="form-control" name="parallel_workers" value="4">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">迁移选项</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="schema_only" id="schema_only">
                                                    <label class="form-check-label" for="schema_only">仅迁移结构</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="data_only" id="data_only">
                                                    <label class="form-check-label" for="data_only">仅迁移数据</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="create_indexes" id="create_indexes" checked>
                                                    <label class="form-check-label" for="create_indexes">创建索引</label>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                
                                <!-- 迁移执行标签页 -->
                                <div class="tab-pane fade" id="migration-tab">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-3">
                                        <button class="btn btn-outline-secondary" onclick="validateConfig()">验证配置</button>
                                        <button class="btn btn-warning" onclick="startMigration(true)">干运行测试</button>
                                        <button class="btn btn-success" onclick="startMigration(false)">开始迁移</button>
                                    </div>
                                    
                                    <div class="progress-container">
                                        <div class="mb-3">
                                            <label class="form-label">总体进度</label>
                                            <div class="progress">
                                                <div class="progress-bar" id="overall-progress" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted" id="progress-text">等待开始...</small>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">当前表进度</label>
                                            <div class="progress">
                                                <div class="progress-bar bg-info" id="table-progress" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted" id="table-progress-text">等待开始...</small>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">迁移统计</div>
                                                <div class="card-body">
                                                    <div class="row text-center">
                                                        <div class="col">
                                                            <h4 id="tables-migrated">0</h4>
                                                            <small>已迁移表</small>
                                                        </div>
                                                        <div class="col">
                                                            <h4 id="rows-migrated">0</h4>
                                                            <small>已迁移行</small>
                                                        </div>
                                                        <div class="col">
                                                            <h4 id="errors-count">0</h4>
                                                            <small>错误数</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">性能指标</div>
                                                <div class="card-body">
                                                    <div class="row text-center">
                                                        <div class="col">
                                                            <h4 id="elapsed-time">00:00:00</h4>
                                                            <small>已用时间</small>
                                                        </div>
                                                        <div class="col">
                                                            <h4 id="avg-speed">0</h4>
                                                            <small>行/秒</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 日志监控标签页 -->
                                <div class="tab-pane fade" id="logs-tab">
                                    <div class="d-flex justify-content-between mb-3">
                                        <h5>实时日志</h5>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">清空日志</button>
                                    </div>
                                    <div class="log-container border rounded p-3 bg-light">
                                        <pre id="log-content"></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">快速操作</div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="loadExampleConfig()">加载示例配置</button>
                                <button class="btn btn-outline-secondary" onclick="saveConfig()">保存配置</button>
                                <button class="btn btn-outline-secondary" onclick="loadConfig()">加载配置</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">系统状态</div>
                        <div class="card-body">
                            <div class="mb-2">
                                <span class="badge bg-secondary status-badge">源数据库</span>
                                <span class="badge bg-danger ms-2" id="source-status">未连接</span>
                            </div>
                            <div class="mb-2">
                                <span class="badge bg-secondary status-badge">目标数据库</span>
                                <span class="badge bg-danger ms-2" id="target-status">未连接</span>
                            </div>
                            <div class="mb-2">
                                <span class="badge bg-secondary status-badge">迁移状态</span>
                                <span class="badge bg-secondary ms-2" id="migration-status">就绪</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // WebSocket连接
            let ws = null;
            let migrationId = null;
            
            function connectWebSocket() {
                ws = new WebSocket(`ws://${window.location.host}/ws`);
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };
                
                ws.onclose = function() {
                    setTimeout(connectWebSocket, 3000); // 重连
                };
            }
            
            function handleWebSocketMessage(data) {
                if (data.type === 'progress') {
                    updateProgress(data);
                } else if (data.type === 'log') {
                    addLogMessage(data.message);
                } else if (data.type === 'status') {
                    updateStatus(data.status);
                }
            }
            
            function updateProgress(data) {
                document.getElementById('overall-progress').style.width = data.overall_progress + '%';
                document.getElementById('table-progress').style.width = data.table_progress + '%';
                document.getElementById('progress-text').textContent = data.overall_text;
                document.getElementById('table-progress-text').textContent = data.table_text;
                
                document.getElementById('tables-migrated').textContent = data.tables_migrated;
                document.getElementById('rows-migrated').textContent = data.rows_migrated;
                document.getElementById('errors-count').textContent = data.errors;
                document.getElementById('elapsed-time').textContent = data.elapsed_time;
                document.getElementById('avg-speed').textContent = data.avg_speed;
            }
            
            function addLogMessage(message) {
                const logContent = document.getElementById('log-content');
                logContent.textContent += new Date().toLocaleTimeString() + ' - ' + message + '\\n';
                logContent.scrollTop = logContent.scrollHeight;
            }
            
            function updateStatus(status) {
                document.getElementById('status').textContent = status;
            }
            
            async function testConnection(type) {
                const form = document.getElementById('migration-form');
                const formData = new FormData(form);
                
                const config = {
                    host: formData.get(type + '_host'),
                    port: parseInt(formData.get(type + '_port')),
                    database: formData.get(type + '_database'),
                    username: formData.get(type + '_username'),
                    password: formData.get(type + '_password'),
                    db_type: formData.get(type + '_db_type')
                };
                
                try {
                    const response = await fetch('/api/test-connection', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({database_config: config})
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        document.getElementById(type + '-status').className = 'badge bg-success ms-2';
                        document.getElementById(type + '-status').textContent = '已连接';
                        alert('连接测试成功！');
                    } else {
                        document.getElementById(type + '-status').className = 'badge bg-danger ms-2';
                        document.getElementById(type + '-status').textContent = '连接失败';
                        alert('连接测试失败：' + result.error);
                    }
                } catch (error) {
                    alert('连接测试失败：' + error.message);
                }
            }
            
            async function startMigration(dryRun = false) {
                const config = getFormConfig();
                
                try {
                    const response = await fetch('/api/migrate', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({config: config, dry_run: dryRun})
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        migrationId = result.migration_id;
                        document.querySelector('.progress-container').style.display = 'block';
                        document.getElementById('migration-status').className = 'badge bg-warning ms-2';
                        document.getElementById('migration-status').textContent = dryRun ? '干运行中' : '迁移中';
                    } else {
                        alert('启动迁移失败：' + result.error);
                    }
                } catch (error) {
                    alert('启动迁移失败：' + error.message);
                }
            }
            
            function getFormConfig() {
                const form = document.getElementById('migration-form');
                const formData = new FormData(form);
                
                return {
                    source: {
                        host: formData.get('source_host'),
                        port: parseInt(formData.get('source_port')),
                        database: formData.get('source_database'),
                        username: formData.get('source_username'),
                        password: formData.get('source_password'),
                        db_type: formData.get('source_db_type')
                    },
                    target: {
                        host: formData.get('target_host'),
                        port: parseInt(formData.get('target_port')),
                        database: formData.get('target_database'),
                        username: formData.get('target_username'),
                        password: formData.get('target_password'),
                        db_type: formData.get('target_db_type')
                    },
                    batch_size: parseInt(formData.get('batch_size')),
                    parallel_workers: parseInt(formData.get('parallel_workers')),
                    schema_only: formData.get('schema_only') === 'on',
                    data_only: formData.get('data_only') === 'on',
                    create_indexes: formData.get('create_indexes') === 'on'
                };
            }
            
            function clearLogs() {
                document.getElementById('log-content').textContent = '';
            }
            
            // 页面加载时连接WebSocket
            window.onload = function() {
                connectWebSocket();
            };
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.post("/api/test-connection")
async def test_connection(request: ConnectionTestRequest):
    """测试数据库连接"""
    try:
        # 转换为内部配置格式
        config = DatabaseConfig(
            host=request.database_config.host,
            port=request.database_config.port,
            database=request.database_config.database,
            username=request.database_config.username,
            password=request.database_config.password,
            db_type=request.database_config.db_type,
            charset=request.database_config.charset,
            ssl_enabled=request.database_config.ssl_enabled
        )
        
        # 测试连接
        db_manager = DatabaseManager(config)
        success = db_manager.test_connection()
        
        if success:
            return {"success": True, "message": "连接成功"}
        else:
            return {"success": False, "error": "连接失败"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/api/migrate")
async def start_migration(request: MigrationRequest):
    """启动迁移任务"""
    try:
        # 生成迁移ID
        migration_id = str(uuid.uuid4())
        
        # 转换配置格式
        source_config = DatabaseConfig(
            host=request.config.source.host,
            port=request.config.source.port,
            database=request.config.source.database,
            username=request.config.source.username,
            password=request.config.source.password,
            db_type=request.config.source.db_type,
            charset=request.config.source.charset,
            ssl_enabled=request.config.source.ssl_enabled
        )
        
        target_config = DatabaseConfig(
            host=request.config.target.host,
            port=request.config.target.port,
            database=request.config.target.database,
            username=request.config.target.username,
            password=request.config.target.password,
            db_type=request.config.target.db_type,
            charset=request.config.target.charset,
            ssl_enabled=request.config.target.ssl_enabled
        )
        
        migration_config = MigrationConfig(
            source=source_config,
            target=target_config,
            batch_size=request.config.batch_size,
            parallel_workers=request.config.parallel_workers,
            schema_only=request.config.schema_only,
            data_only=request.config.data_only,
            create_indexes=request.config.create_indexes,
            create_foreign_keys=request.config.create_foreign_keys,
            tables=request.config.tables,
            exclude_tables=request.config.exclude_tables
        )
        
        # 创建迁移引擎
        if source_config.db_type in ['mysql', 'postgresql'] and target_config.db_type in ['mysql', 'postgresql']:
            engine = AsyncMigrationEngine(migration_config)
        else:
            # 回退到同步引擎
            from .migrator import MigrationEngine
            engine = MigrationEngine(migration_config)
        
        # 保存到活动迁移列表
        active_migrations[migration_id] = engine
        
        # 启动后台迁移任务
        asyncio.create_task(run_migration_task(migration_id, engine, request.dry_run))
        
        return {"success": True, "migration_id": migration_id}
        
    except Exception as e:
        logger.error(f"启动迁移失败: {str(e)}")
        return {"success": False, "error": str(e)}


async def run_migration_task(migration_id: str, engine, dry_run: bool):
    """运行迁移任务"""
    try:
        # 设置进度回调
        def progress_callback(table_name, current, total):
            asyncio.create_task(broadcast_progress(migration_id, table_name, current, total))
        
        if hasattr(engine, 'set_progress_callback'):
            engine.set_progress_callback(progress_callback)
        
        # 执行迁移
        if isinstance(engine, AsyncMigrationEngine):
            stats = await engine.migrate_async()
        else:
            # 在线程池中运行同步迁移
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                stats = await asyncio.get_event_loop().run_in_executor(
                    executor, engine.migrate
                )
        
        # 广播完成状态
        await broadcast_completion(migration_id, stats)
        
    except Exception as e:
        logger.error(f"迁移任务失败: {str(e)}")
        await broadcast_error(migration_id, str(e))
    
    finally:
        # 清理
        if migration_id in active_migrations:
            del active_migrations[migration_id]


async def broadcast_progress(migration_id: str, table_name: str, current: int, total: int):
    """广播进度更新"""
    message = {
        "type": "progress",
        "migration_id": migration_id,
        "table_name": table_name,
        "current": current,
        "total": total,
        "table_progress": (current / total * 100) if total > 0 else 0
    }
    
    await broadcast_to_websockets(message)


async def broadcast_completion(migration_id: str, stats):
    """广播完成状态"""
    message = {
        "type": "completion",
        "migration_id": migration_id,
        "stats": {
            "tables_migrated": stats.tables_migrated,
            "rows_migrated": stats.rows_migrated,
            "errors": stats.errors,
            "duration": stats.end_time - stats.start_time if stats.end_time else 0
        }
    }
    
    await broadcast_to_websockets(message)


async def broadcast_error(migration_id: str, error: str):
    """广播错误信息"""
    message = {
        "type": "error",
        "migration_id": migration_id,
        "error": error
    }
    
    await broadcast_to_websockets(message)


async def broadcast_to_websockets(message: dict):
    """向所有WebSocket连接广播消息"""
    if websocket_connections:
        message_str = json.dumps(message)
        for ws in websocket_connections.copy():
            try:
                await ws.send_text(message_str)
            except:
                websocket_connections.remove(ws)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点"""
    await websocket.accept()
    websocket_connections.append(websocket)
    
    try:
        while True:
            # 保持连接活跃
            await websocket.receive_text()
    except WebSocketDisconnect:
        websocket_connections.remove(websocket)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
