#!/bin/bash
# RDS Migration Tool 部署脚本
# 企业级数据库迁移工具自动化部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
NAMESPACE="rds-migrate"
APP_NAME="rds-migration-tool"
VERSION="2.0.0"
DOCKER_REGISTRY="your-registry.com"
KUBECONFIG_PATH="${KUBECONFIG:-$HOME/.kube/config}"

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "检查部署前提条件..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查Helm（可选）
    if command -v helm &> /dev/null; then
        log_info "检测到Helm，将使用Helm进行部署"
        USE_HELM=true
    else
        log_warning "未检测到Helm，将使用kubectl进行部署"
        USE_HELM=false
    fi
    
    log_success "前提条件检查完成"
}

build_docker_image() {
    log_info "构建Docker镜像..."
    
    # 构建镜像
    docker build -t "${APP_NAME}:${VERSION}" .
    docker build -t "${APP_NAME}:latest" .
    
    # 如果指定了registry，推送镜像
    if [ -n "$DOCKER_REGISTRY" ] && [ "$DOCKER_REGISTRY" != "your-registry.com" ]; then
        log_info "推送镜像到registry..."
        docker tag "${APP_NAME}:${VERSION}" "${DOCKER_REGISTRY}/${APP_NAME}:${VERSION}"
        docker tag "${APP_NAME}:latest" "${DOCKER_REGISTRY}/${APP_NAME}:latest"
        docker push "${DOCKER_REGISTRY}/${APP_NAME}:${VERSION}"
        docker push "${DOCKER_REGISTRY}/${APP_NAME}:latest"
    fi
    
    log_success "Docker镜像构建完成"
}

create_namespace() {
    log_info "创建Kubernetes命名空间..."
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 已存在"
    else
        kubectl apply -f k8s/namespace.yaml
        log_success "命名空间 $NAMESPACE 创建成功"
    fi
}

deploy_secrets() {
    log_info "部署Secrets..."
    
    # 检查是否需要生成新的secrets
    if [ "$1" = "--generate-secrets" ]; then
        log_info "生成新的安全凭据..."
        
        # 生成随机密码
        POSTGRES_PASSWORD=$(openssl rand -base64 32)
        REDIS_PASSWORD=$(openssl rand -base64 32)
        APP_SECRET_KEY=$(openssl rand -base64 64)
        ENCRYPTION_KEY=$(openssl rand -base64 32)
        JWT_SECRET=$(openssl rand -base64 64)
        
        # 创建临时secrets文件
        cat > /tmp/secrets.yaml << EOF
apiVersion: v1
kind: Secret
metadata:
  name: rds-migrate-secrets
  namespace: $NAMESPACE
type: Opaque
data:
  postgres-password: $(echo -n "$POSTGRES_PASSWORD" | base64 -w 0)
  redis-password: $(echo -n "$REDIS_PASSWORD" | base64 -w 0)
  app-secret-key: $(echo -n "$APP_SECRET_KEY" | base64 -w 0)
  encryption-key: $(echo -n "$ENCRYPTION_KEY" | base64 -w 0)
  jwt-secret: $(echo -n "$JWT_SECRET" | base64 -w 0)
EOF
        
        kubectl apply -f /tmp/secrets.yaml
        rm /tmp/secrets.yaml
        
        log_success "新的安全凭据已生成并部署"
    else
        kubectl apply -f k8s/secrets.yaml
        log_success "Secrets部署完成"
    fi
}

deploy_configmaps() {
    log_info "部署ConfigMaps..."
    kubectl apply -f k8s/configmap.yaml
    log_success "ConfigMaps部署完成"
}

deploy_rbac() {
    log_info "部署RBAC配置..."
    kubectl apply -f k8s/rbac.yaml
    log_success "RBAC配置部署完成"
}

deploy_storage() {
    log_info "部署存储配置..."
    kubectl apply -f k8s/pvc.yaml
    log_success "存储配置部署完成"
}

deploy_applications() {
    log_info "部署应用程序..."
    
    # 部署应用
    kubectl apply -f k8s/deployment.yaml
    
    # 等待部署完成
    log_info "等待应用程序启动..."
    kubectl wait --for=condition=available --timeout=300s deployment/rds-migrate-app -n "$NAMESPACE"
    kubectl wait --for=condition=available --timeout=300s deployment/postgres -n "$NAMESPACE"
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n "$NAMESPACE"
    
    log_success "应用程序部署完成"
}

deploy_services() {
    log_info "部署Services..."
    kubectl apply -f k8s/service.yaml
    log_success "Services部署完成"
}

deploy_ingress() {
    log_info "部署Ingress..."
    
    # 检查Ingress Controller
    if kubectl get ingressclass nginx &> /dev/null; then
        kubectl apply -f k8s/ingress.yaml
        log_success "Ingress部署完成"
    else
        log_warning "未检测到Nginx Ingress Controller，跳过Ingress部署"
    fi
}

deploy_autoscaling() {
    log_info "部署自动扩缩容配置..."
    
    # 检查Metrics Server
    if kubectl get apiservice v1beta1.metrics.k8s.io &> /dev/null; then
        kubectl apply -f k8s/hpa.yaml
        log_success "自动扩缩容配置部署完成"
    else
        log_warning "未检测到Metrics Server，跳过HPA部署"
    fi
}

verify_deployment() {
    log_info "验证部署状态..."
    
    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n "$NAMESPACE"
    
    # 检查Service状态
    log_info "检查Service状态..."
    kubectl get services -n "$NAMESPACE"
    
    # 检查Ingress状态
    log_info "检查Ingress状态..."
    kubectl get ingress -n "$NAMESPACE" 2>/dev/null || log_warning "未部署Ingress"
    
    # 健康检查
    log_info "执行健康检查..."
    if kubectl exec -n "$NAMESPACE" deployment/rds-migrate-app -- curl -f http://localhost:8000/health &> /dev/null; then
        log_success "应用程序健康检查通过"
    else
        log_error "应用程序健康检查失败"
        return 1
    fi
    
    log_success "部署验证完成"
}

show_access_info() {
    log_info "获取访问信息..."
    
    # 获取Service信息
    CLUSTER_IP=$(kubectl get service rds-migrate-service -n "$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
    
    # 获取LoadBalancer信息（如果存在）
    EXTERNAL_IP=$(kubectl get service rds-migrate-lb -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    
    # 获取Ingress信息（如果存在）
    INGRESS_HOST=$(kubectl get ingress rds-migrate-ingress -n "$NAMESPACE" -o jsonpath='{.spec.rules[0].host}' 2>/dev/null || echo "")
    
    echo ""
    echo "=========================================="
    echo "RDS Migration Tool 部署完成！"
    echo "=========================================="
    echo ""
    echo "访问方式："
    echo "1. 集群内部访问: http://$CLUSTER_IP:8000"
    
    if [ -n "$EXTERNAL_IP" ]; then
        echo "2. 外部LoadBalancer访问: http://$EXTERNAL_IP"
    fi
    
    if [ -n "$INGRESS_HOST" ]; then
        echo "3. Ingress域名访问: https://$INGRESS_HOST"
    fi
    
    echo ""
    echo "端口转发访问（用于测试）："
    echo "kubectl port-forward -n $NAMESPACE service/rds-migrate-service 8000:8000"
    echo "然后访问: http://localhost:8000"
    echo ""
    echo "查看日志："
    echo "kubectl logs -n $NAMESPACE deployment/rds-migrate-app -f"
    echo ""
    echo "=========================================="
}

cleanup() {
    log_info "清理部署..."
    
    read -p "确定要删除整个部署吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        kubectl delete namespace "$NAMESPACE"
        log_success "部署已清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 主函数
main() {
    case "$1" in
        "build")
            check_prerequisites
            build_docker_image
            ;;
        "deploy")
            check_prerequisites
            create_namespace
            deploy_secrets "$2"
            deploy_configmaps
            deploy_rbac
            deploy_storage
            deploy_applications
            deploy_services
            deploy_ingress
            deploy_autoscaling
            verify_deployment
            show_access_info
            ;;
        "update")
            check_prerequisites
            build_docker_image
            kubectl rollout restart deployment/rds-migrate-app -n "$NAMESPACE"
            kubectl rollout status deployment/rds-migrate-app -n "$NAMESPACE"
            verify_deployment
            ;;
        "cleanup")
            cleanup
            ;;
        "status")
            verify_deployment
            show_access_info
            ;;
        *)
            echo "用法: $0 {build|deploy|update|cleanup|status}"
            echo ""
            echo "命令说明："
            echo "  build                    - 构建Docker镜像"
            echo "  deploy [--generate-secrets] - 完整部署应用"
            echo "  update                   - 更新应用"
            echo "  cleanup                  - 清理部署"
            echo "  status                   - 查看部署状态"
            echo ""
            echo "示例："
            echo "  $0 build                 # 构建镜像"
            echo "  $0 deploy                # 部署应用"
            echo "  $0 deploy --generate-secrets # 部署并生成新密钥"
            echo "  $0 update                # 更新应用"
            echo "  $0 status                # 查看状态"
            echo "  $0 cleanup               # 清理部署"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
